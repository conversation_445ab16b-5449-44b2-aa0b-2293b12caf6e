@echo off
echo ========================================
echo   通过JavaWebSpringBootApplication启动
echo ========================================

REM 设置编码
chcp 65001 > nul

REM 进入项目目录
cd /d "%~dp0"

echo 🔧 项目目录: %CD%
echo 🔧 主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo.

REM 检查编译后的类文件是否存在
if not exist "target\classes\cn\gzsf\javawebspringboot\JavaWebSpringBootApplication.class" (
    echo ❌ 主类文件不存在，开始编译...
    echo.
    
    REM 使用Maven编译
    if exist ".\apache-maven-3.9.4\bin\mvn.cmd" (
        echo 📦 使用Maven编译项目...
        .\apache-maven-3.9.4\bin\mvn.cmd compile
        if errorlevel 1 (
            echo ❌ Maven编译失败！
            pause
            exit /b 1
        )
    ) else (
        echo ❌ 找不到Maven，请先编译项目
        pause
        exit /b 1
    )
)

echo ✅ 编译完成，准备启动...
echo.

REM 设置Java环境变量（如果需要）
REM set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_202
REM set PATH=%JAVA_HOME%\bin;%PATH%

REM 检查Java版本
echo 🔍 检查Java环境:
java -version
echo.

REM 构建类路径
echo 📚 构建类路径...
set CLASSPATH=target\classes

REM 添加所有依赖jar包到类路径
if exist "target\dependency" (
    for %%i in (target\dependency\*.jar) do (
        set CLASSPATH=!CLASSPATH!;%%i
    )
    echo ✅ 已添加依赖jar包
) else (
    echo ⚠️ 依赖目录不存在，尝试使用Maven依赖
    REM 如果没有dependency目录，尝试复制依赖
    if exist ".\apache-maven-3.9.4\bin\mvn.cmd" (
        echo 📦 复制Maven依赖...
        .\apache-maven-3.9.4\bin\mvn.cmd dependency:copy-dependencies -DoutputDirectory=target/dependency
    )
    
    REM 重新构建类路径
    set CLASSPATH=target\classes
    for %%i in (target\dependency\*.jar) do (
        set CLASSPATH=!CLASSPATH!;%%i
    )
)

echo.
echo 🚀 启动SpringBoot应用程序...
echo    主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo    端口: 8082
echo    访问地址: http://localhost:8082
echo.

REM 设置JVM参数
set JVM_OPTS=-Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Dspring.profiles.active=dev
set JVM_OPTS=%JVM_OPTS% -Dserver.port=8082
set JVM_OPTS=%JVM_OPTS% -Dspring.output.ansi.enabled=always

echo 🌟 正在启动，请稍候...
echo    按 Ctrl+C 可停止应用程序
echo.

REM 启动主类
java %JVM_OPTS% -cp "%CLASSPATH%" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

echo.
echo 📴 应用程序已停止
pause
