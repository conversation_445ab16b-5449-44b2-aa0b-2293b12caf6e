@echo off
echo ========================================
echo     Java 1.8 环境设置和项目启动
echo ========================================

REM 设置编码
chcp 65001 > nul

REM 进入项目目录
cd /d "%~dp0"

echo 🔧 项目目录: %CD%
echo.

REM 检查是否有便携版Java 1.8
if exist "jdk1.8" (
    echo ✅ 找到便携版Java 1.8
    set JAVA_HOME=%CD%\jdk1.8
    set PATH=%JAVA_HOME%\bin;%PATH%
    goto :start_app
)

REM 检查常见的Java 1.8安装位置
echo 🔍 搜索Java 1.8安装...

set JAVA8_PATHS[0]=C:\Program Files\Java\jdk1.8.0_202
set JAVA8_PATHS[1]=C:\Program Files\Java\jdk1.8.0_301
set JAVA8_PATHS[2]=C:\Program Files\Java\jdk1.8.0_311
set JAVA8_PATHS[3]=C:\Program Files\Java\jdk1.8.0_321
set JAVA8_PATHS[4]=C:\Program Files\Java\jdk1.8.0_331
set JAVA8_PATHS[5]=C:\Program Files (x86)\Java\jdk1.8.0_202
set JAVA8_PATHS[6]=C:\Program Files (x86)\Java\jdk1.8.0_301

for /L %%i in (0,1,6) do (
    call set "CURRENT_PATH=%%JAVA8_PATHS[%%i]%%"
    if exist "!CURRENT_PATH!" (
        echo ✅ 找到Java 1.8: !CURRENT_PATH!
        set JAVA_HOME=!CURRENT_PATH!
        set PATH=!CURRENT_PATH!\bin;%PATH%
        goto :start_app
    )
)

echo ⚠️ 未找到Java 1.8，将使用当前Java版本
echo 当前Java版本:
java -version
echo.

REM 询问是否继续
set /p CONTINUE="是否继续使用当前Java版本？(y/n): "
if /i "%CONTINUE%" neq "y" (
    echo 请安装Java 1.8后重试
    pause
    exit /b 1
)

:start_app
echo.
echo 🔧 Java环境设置完成
if defined JAVA_HOME (
    echo JAVA_HOME: %JAVA_HOME%
)
echo.

REM 检查Java版本
echo 🔍 当前Java版本:
java -version
echo.

REM 检查编译状态
if not exist "target\classes\cn\gzsf\javawebspringboot\JavaWebSpringBootApplication.class" (
    echo 📦 主类不存在，开始编译...
    if exist ".\apache-maven-3.9.4\bin\mvn.cmd" (
        .\apache-maven-3.9.4\bin\mvn.cmd clean compile
        if errorlevel 1 (
            echo ❌ 编译失败
            pause
            exit /b 1
        )
    ) else (
        echo ❌ 找不到Maven，请手动编译项目
        pause
        exit /b 1
    )
)

echo ✅ 编译完成
echo.

REM 复制依赖（如果需要）
if not exist "target\dependency" (
    echo 📚 复制Maven依赖...
    if defined JAVA_HOME (
        .\apache-maven-3.9.4\bin\mvn.cmd dependency:copy-dependencies -DoutputDirectory=target/dependency
    )
)

REM 构建类路径
echo 📚 构建类路径...
set CLASSPATH=target\classes

if exist "target\dependency" (
    for %%i in (target\dependency\*.jar) do (
        call set CLASSPATH=%%CLASSPATH%%;%%i
    )
    echo ✅ 依赖已添加到类路径
)

echo.
echo 🚀 启动SpringBoot应用程序...
echo    主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo    端口: 8082
echo    访问地址: http://localhost:8082
echo.

REM JVM参数
set JVM_OPTS=-Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Dserver.port=8082
set JVM_OPTS=%JVM_OPTS% -Dspring.profiles.active=dev
set JVM_OPTS=%JVM_OPTS% -Xms256m -Xmx512m

echo 🌟 正在启动，请稍候...
echo    按 Ctrl+C 停止应用程序
echo.

REM 启动应用程序
java %JVM_OPTS% -cp "%CLASSPATH%" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

echo.
echo 📴 应用程序已停止
pause
