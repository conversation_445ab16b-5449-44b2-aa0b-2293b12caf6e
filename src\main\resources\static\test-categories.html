<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .category-item {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            border: none;
            transition: background 0.3s;
        }
        .category-item:hover {
            background: #0056b3;
        }
        .category-item.active {
            background: #28a745;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .product-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .product-card .price {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.2em;
        }
        .loading {
            text-align: center;
            color: #007bff;
            font-style: italic;
            padding: 20px;
        }
        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 分类功能测试页面</h1>
        
        <div>
            <h3>📋 分类列表</h3>
            <div id="categoryList" class="category-list">
                <button class="category-item active" data-category="all" data-category-id="0">全部</button>
            </div>
        </div>

        <div>
            <h3>📊 调试信息</h3>
            <div id="debugInfo" class="result"></div>
        </div>

        <div>
            <h3>🛍️ 产品展示</h3>
            <div id="productGrid" class="product-grid">
                <div class="loading">加载中...</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        let categoriesData = [];
        let currentCategory = 'all';

        // 页面加载时初始化
        window.onload = function() {
            loadCategories();
            loadProducts('all', 0);
        };

        // 加载分类数据
        async function loadCategories() {
            try {
                const response = await axios.get('/admin/category/all');
                categoriesData = response.data;
                
                updateDebugInfo(`分类数据加载成功:\n${JSON.stringify(categoriesData, null, 2)}`);
                
                renderCategories(categoriesData);
            } catch (error) {
                updateDebugInfo(`分类数据加载失败: ${error.message}`);
            }
        }

        // 渲染分类按钮
        function renderCategories(categories) {
            const categoryList = document.getElementById('categoryList');
            
            // 保留"全部"按钮
            categoryList.innerHTML = `
                <button class="category-item active" data-category="all" data-category-id="0" onclick="selectCategory('all', 0)">全部</button>
            `;
            
            categories.forEach(category => {
                const button = document.createElement('button');
                button.className = 'category-item';
                button.setAttribute('data-category', category.name);
                button.setAttribute('data-category-id', category.id);
                button.textContent = category.name;
                button.onclick = () => selectCategory(category.name, category.id);
                categoryList.appendChild(button);
                
                console.log(`渲染分类: ${category.name} (ID: ${category.id})`);
            });
        }

        // 选择分类
        function selectCategory(categoryName, categoryId) {
            // 更新按钮状态
            document.querySelectorAll('.category-item').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentCategory = categoryName;
            
            updateDebugInfo(`选择分类: ${categoryName} (ID: ${categoryId})`);
            
            // 加载该分类的产品
            loadProducts(categoryName, categoryId);
        }

        // 加载产品
        async function loadProducts(categoryName, categoryId) {
            const productGrid = document.getElementById('productGrid');
            productGrid.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const response = await axios.get(`/products/category/${categoryId}`);
                
                updateDebugInfo(`产品加载结果:\n分类: ${categoryName} (ID: ${categoryId})\n成功: ${response.data.success}\n产品数量: ${response.data.data?.length || 0}\n\n响应数据:\n${JSON.stringify(response.data, null, 2)}`);
                
                if (response.data.success) {
                    renderProducts(response.data.data);
                } else {
                    productGrid.innerHTML = `<div class="error">加载失败: ${response.data.message}</div>`;
                }
            } catch (error) {
                updateDebugInfo(`产品加载失败: ${error.message}`);
                productGrid.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }

        // 渲染产品
        function renderProducts(products) {
            const productGrid = document.getElementById('productGrid');
            
            if (products.length === 0) {
                productGrid.innerHTML = '<div class="error">暂无产品</div>';
                return;
            }
            
            productGrid.innerHTML = '';
            
            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'product-card';
                productCard.innerHTML = `
                    <h3>${product.name}</h3>
                    <p>${product.description || '暂无描述'}</p>
                    <div class="price">¥${product.price}</div>
                    <small>库存: ${product.stock || 0}</small>
                `;
                productGrid.appendChild(productCard);
            });
        }

        // 更新调试信息
        function updateDebugInfo(info) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.textContent = info;
            console.log(info);
        }
    </script>
</body>
</html>
