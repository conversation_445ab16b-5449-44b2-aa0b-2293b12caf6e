package cn.gzsf.javawebspringboot.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 安全配置类
 * 用于限制敏感接口的访问
 */
@Configuration
public class SecurityConfig implements WebMvcConfigurer {

    @Value("${app.debug.enabled:false}")
    private boolean debugEnabled;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加调试接口拦截器
        registry.addInterceptor(new DebugInterceptor())
                .addPathPatterns("/fix/**", "/debug/**", "/test/**");
    }

    /**
     * 调试接口拦截器
     * 在生产环境中阻止访问调试和修复接口
     */
    private class DebugInterceptor implements HandlerInterceptor {
        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            // 检查是否启用调试模式
            if (!debugEnabled) {
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"调试接口已禁用\",\"code\":403}");
                return false;
            }
            
            // 记录调试接口访问日志
            System.out.println("⚠️ 调试接口访问: " + request.getRequestURI() + " - IP: " + getClientIP(request));
            return true;
        }
        
        private String getClientIP(HttpServletRequest request) {
            String xForwardedFor = request.getHeader("X-Forwarded-For");
            if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
                return xForwardedFor.split(",")[0].trim();
            }
            
            String xRealIP = request.getHeader("X-Real-IP");
            if (xRealIP != null && !xRealIP.isEmpty()) {
                return xRealIP;
            }
            
            return request.getRemoteAddr();
        }
    }
}
