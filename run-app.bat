@echo off
echo Starting Spring Boot Application...

REM 设置Java路径（如果需要）
REM set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_202

REM 编译Java代码（如果需要）
echo Compiling Java sources...
javac -cp "apache-maven-3.9.4\lib\*" -d target\classes src\main\java\cn\gzsf\javawebspringboot\*.java src\main\java\cn\gzsf\javawebspringboot\controller\*.java src\main\java\cn\gzsf\javawebspringboot\entity\*.java src\main\java\cn\gzsf\javawebspringboot\service\*.java src\main\java\cn\gzsf\javawebspringboot\dao\*.java

REM 运行应用
echo Running Spring Boot Application...
java -cp "target\classes;apache-maven-3.9.4\lib\*" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

pause
