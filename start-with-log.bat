@echo off
echo ================================
echo SpringBoot Startup with Logging
echo ================================

REM 清除可能冲突的环境变量
set JDK-12=

REM 设置JDK 1.8路径
set "JAVA_HOME=D:\Program Files (x86)\JAVA\jdk"
set "MAVEN_HOME=%cd%\apache-maven-3.9.4"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%
echo.

echo Testing Java...
"%JAVA_HOME%\bin\java.exe" -version
echo.

echo Testing Maven...
"%MAVEN_HOME%\bin\mvn.cmd" --version
echo.

echo Starting SpringBoot application...
echo Output will be logged to startup.log
echo Port: 8082
echo URL: http://localhost:8082
echo ================================

echo Starting Maven... > startup.log
"%MAVEN_HOME%\bin\mvn.cmd" spring-boot:run >> startup.log 2>&1

echo.
echo Application ended. Check startup.log for details.
pause
