# 启动SpringBoot应用
Write-Host "🚀 启动SpringBoot应用..." -ForegroundColor Green

# 查找jar文件
$jarFiles = Get-ChildItem -Recurse -Filter "*.jar" | Where-Object {$_.Name -like "*SpringBoot*" -or $_.Name -like "*SNAPSHOT*"}

if ($jarFiles) {
    $jarFile = $jarFiles[0].FullName
    Write-Host "📦 找到jar文件: $jarFile" -ForegroundColor Cyan
    
    Write-Host "🎯 启动应用..." -ForegroundColor Yellow
    java -jar $jarFile
} else {
    Write-Host "❌ 未找到jar文件，尝试直接运行..." -ForegroundColor Yellow
    
    # 设置classpath
    $classpath = "target\classes"
    
    # 添加所有jar依赖
    $allJars = Get-ChildItem -Recurse -Filter "*.jar" | ForEach-Object { $_.FullName }
    if ($allJars) {
        $classpath += ";" + ($allJars -join ";")
    }
    
    Write-Host "📦 Classpath: $classpath" -ForegroundColor Cyan
    Write-Host "🎯 启动应用..." -ForegroundColor Yellow
    
    java -cp $classpath cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
}
