@echo off
echo ========================================
echo      SpringBoot简单启动 (IDEA风格)
echo ========================================

REM 设置编码
chcp 65001 > nul

REM 进入项目目录
cd /d "%~dp0"

echo 🔧 当前目录: %CD%
echo 🔧 启动SpringBoot应用程序...
echo.

REM 直接使用Java启动，类似IDEA的方式
java -Dfile.encoding=UTF-8 ^
     -Dspring.profiles.active=dev ^
     -Dserver.port=8082 ^
     -cp "target/classes" ^
     cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

pause
