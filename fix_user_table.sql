-- 修复用户表问题
-- 检查并创建user表（如果不存在）

-- 创建user表
CREATE TABLE IF NOT EXISTS `user` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
  `user_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户账号',
  `username` VARCHAR(100) NOT NULL COMMENT '用户名',
  `phone` VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `avatar` VARCHAR(255) DEFAULT '/images/avatar/default-avatar.jpg' COMMENT '用户头像',
  `signature` VARCHAR(500) DEFAULT '' COMMENT '用户签名',
  `register_time` BIGINT NOT NULL COMMENT '注册时间',
  `created_time` BIGINT DEFAULT 0 COMMENT '创建时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_phone` (`phone`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';

-- 如果存在users表，将数据迁移到user表
INSERT IGNORE INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`, `created_time`)
SELECT `user_id`, `username`, `phone`, `password`, `register_time`, `register_time`
FROM `users`
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'users');

-- 插入一些测试用户数据（如果表为空）
INSERT IGNORE INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`, `created_time`) VALUES
('admin', '管理员', '15120248009', 'admin123', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user001', '小雯', '18823912577', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user002', 'KKKK', '18796247689', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user003', '测试用户1', '13800138001', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user004', '测试用户2', '13800138002', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 更新created_time字段（如果为0）
UPDATE `user` SET `created_time` = `register_time` WHERE `created_time` = 0;

-- 显示创建结果
SELECT COUNT(*) as user_count FROM `user`;
