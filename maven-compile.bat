@echo off
echo Compiling SpringBoot project with Maven...
echo ==========================================

REM 设置Maven路径
set MAVEN_HOME=%cd%\apache-maven-3.9.4
set PATH=%MAVEN_HOME%\bin;%PATH%

REM 设置Java路径
set JAVA_HOME=C:\Program Files\Common Files\Oracle\Java\javapath

echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%

REM 检查Java版本
echo Checking Java version...
java -version

REM 编译项目
echo Compiling project...
mvn clean compile

echo Compilation completed!
pause
