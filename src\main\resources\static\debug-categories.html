<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 分类产品关联调试工具</h1>
        
        <div class="section">
            <h3>📊 调试信息</h3>
            <button class="btn" onclick="debugCategories()">获取分类调试信息</button>
            <button class="btn" onclick="getActualCategories()">获取实际分类数据</button>
            <button class="btn success" onclick="fixCategories()">修复产品分类关联</button>
            <button class="btn" onclick="testCategoryAPI()">测试分类API</button>
            <div id="debugResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>🧪 分类产品测试</h3>
            <p>测试各个分类的产品加载：</p>
            <button class="btn" onclick="testCategory(0, '全部')">全部产品</button>
            <button class="btn" onclick="testCategory(1, '护肤品')">护肤品</button>
            <button class="btn" onclick="testCategory(2, '彩妆')">彩妆</button>
            <button class="btn" onclick="testCategory(3, '香水')">香水</button>
            <button class="btn" onclick="testCategory(4, '工具')">工具</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>📋 分类列表</h3>
            <button class="btn" onclick="loadCategories()">加载分类列表</button>
            <div id="categoriesResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = content;
        }

        function showLoading(elementId) {
            showResult(elementId, '加载中...', 'loading');
        }

        async function debugCategories() {
            showLoading('debugResult');
            try {
                const response = await axios.get('/fix/debug-categories');
                showResult('debugResult', JSON.stringify(response.data, null, 2), 'success');
            } catch (error) {
                showResult('debugResult', `错误: ${error.message}\n${error.response?.data || ''}`, 'error');
            }
        }

        async function fixCategories() {
            showLoading('debugResult');
            try {
                const response = await axios.post('/fix/fix-product-categories');
                showResult('debugResult', JSON.stringify(response.data, null, 2), 'success');
            } catch (error) {
                showResult('debugResult', `错误: ${error.message}\n${error.response?.data || ''}`, 'error');
            }
        }

        async function getActualCategories() {
            showLoading('debugResult');
            try {
                const response = await axios.get('/fix/actual-categories');
                showResult('debugResult', JSON.stringify(response.data, null, 2), 'success');
            } catch (error) {
                showResult('debugResult', `获取实际分类数据失败: ${error.message}`, 'error');
            }
        }

        async function testCategoryAPI() {
            showLoading('debugResult');
            try {
                const response = await axios.get('/admin/category/all');
                showResult('debugResult', `分类API测试成功:\n${JSON.stringify(response.data, null, 2)}`, 'success');
            } catch (error) {
                showResult('debugResult', `分类API测试失败: ${error.message}`, 'error');
            }
        }

        async function testCategory(categoryId, categoryName) {
            showLoading('testResult');
            try {
                const response = await axios.get(`/products/category/${categoryId}`);
                const result = `分类: ${categoryName} (ID: ${categoryId})\n` +
                              `成功: ${response.data.success}\n` +
                              `产品数量: ${response.data.data?.length || 0}\n` +
                              `消息: ${response.data.message}\n\n` +
                              `产品列表:\n${JSON.stringify(response.data.data, null, 2)}`;
                showResult('testResult', result, response.data.success ? 'success' : 'error');
            } catch (error) {
                showResult('testResult', `测试分类 ${categoryName} 失败: ${error.message}`, 'error');
            }
        }

        async function loadCategories() {
            showLoading('categoriesResult');
            try {
                const response = await axios.get('/admin/category/all');
                showResult('categoriesResult', JSON.stringify(response.data, null, 2), 'success');
            } catch (error) {
                showResult('categoriesResult', `加载分类失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动获取调试信息
        window.onload = function() {
            debugCategories();
        };
    </script>
</body>
</html>
