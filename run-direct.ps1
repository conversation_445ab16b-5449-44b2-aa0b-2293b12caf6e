# 直接运行SpringBoot应用（类似IDEA的Run方式）
Write-Host "🚀 直接启动SpringBoot应用..." -ForegroundColor Green

# 设置服务器端口
$env:SERVER_PORT = "8082"

# 确保资源文件是最新的
Write-Host "📁 更新资源文件..." -ForegroundColor Yellow
Copy-Item -Recurse -Force "src\main\resources\*" "target\classes\"

# 设置classpath（包含所有必要的依赖）
$classpath = "target\classes"

# 添加Spring Boot相关的jar包（如果有的话）
if (Test-Path "lib") {
    $libJars = Get-ChildItem "lib\*.jar" | ForEach-Object { $_.FullName }
    if ($libJars) {
        $classpath += ";" + ($libJars -join ";")
    }
}

Write-Host "📦 Classpath: $classpath" -ForegroundColor Cyan

# 运行SpringBoot应用
Write-Host "🎯 启动SpringBoot应用..." -ForegroundColor Green
Write-Host "📍 应用将在 http://localhost:8082 启动" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 项目修改已完成:" -ForegroundColor Magenta
Write-Host "   ✅ 息壤臻选区域只显示新品（横向滚动）" -ForegroundColor White
Write-Host "   ✅ 分类导航下方显示分类产品（网格布局）" -ForegroundColor White
Write-Host "   ✅ 清理了所有404错误和死数据引用" -ForegroundColor White
Write-Host ""
Write-Host "⏳ 正在启动应用，请稍候..." -ForegroundColor Yellow

# 直接运行主类
java -cp $classpath -Dserver.port=8082 cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
