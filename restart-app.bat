@echo off
echo ========================================
echo        快速重启SpringBoot应用
echo ========================================
echo.

REM 设置编码
chcp 65001 > nul

echo 🔄 正在停止现有应用程序...

REM 查找并停止占用8082端口的进程
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8082') do (
    echo 🛑 停止进程 %%a
    taskkill /PID %%a /F > nul 2>&1
)

echo ✅ 进程已停止
echo.

REM 等待2秒
timeout /t 2 /nobreak > nul

echo 🚀 重新启动应用程序...
echo.

REM 启动开发模式
call start-dev-mode.bat
