# VSCode Spring Boot 启动脚本
Write-Host "🚀 启动Spring Boot应用..." -ForegroundColor Green

# 检查Java版本
Write-Host "📋 检查Java环境..." -ForegroundColor Yellow
java -version

# 设置环境变量
$env:JAVA_OPTS = "-Dserver.port=8082"

# 编译项目（如果需要）
Write-Host "🔨 编译项目..." -ForegroundColor Yellow
if (Test-Path "target/classes") {
    Write-Host "✅ target/classes 已存在" -ForegroundColor Green
} else {
    Write-Host "❌ target/classes 不存在，需要编译" -ForegroundColor Red
    mkdir -p target/classes
}

# 复制资源文件
Write-Host "📁 复制资源文件..." -ForegroundColor Yellow
Copy-Item -Recurse -Force "src/main/resources/*" "target/classes/"

# 运行Spring Boot应用
Write-Host "🎯 启动Spring Boot应用..." -ForegroundColor Green
Write-Host "📍 应用将在 http://localhost:8082 启动" -ForegroundColor Cyan

# 使用Java直接运行主类
java -cp "target/classes" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

Write-Host "🛑 应用已停止" -ForegroundColor Red
