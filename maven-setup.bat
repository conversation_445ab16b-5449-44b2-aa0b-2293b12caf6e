@echo off
echo Setting up Maven environment...
echo ================================

REM 设置Maven和Java路径
set MAVEN_HOME=%cd%\apache-maven-3.9.4
set JAVA_HOME=C:\Program Files\Common Files\Oracle\Java\javapath
set PATH=%MAVEN_HOME%\bin;%JAVA_HOME%;%PATH%

echo MAVEN_HOME: %MAVEN_HOME%
echo JAVA_HOME: %JAVA_HOME%
echo.

echo Testing Maven...
"%MAVEN_HOME%\bin\mvn.cmd" --version
echo.

echo Downloading dependencies and compiling...
"%MAVEN_HOME%\bin\mvn.cmd" dependency:resolve compile

echo.
echo Setup completed!
pause
