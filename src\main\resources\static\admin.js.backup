new Vue({
    el: '#app',
    data() {
        return {
        // 下拉菜单显示状态
        dropdownVisible: false,
        // 初始激活仪表盘页面
        activeTab: 'dashboard',
        // 用户列表
        userList: [],
        // 对话框显示状态
        dialogVisible: false,
        // 表单数据
        formData: {
            userId: '',
            username: '',
            phone: '',
            password: ''
        },
        // 搜索表单数据
        searchForm: {
            userId: '',
            username: ''
        },
        // 用户分页信息
        userPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 产品分页信息
        productPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 分类分页信息
        categoryPagination: {
            currentPage: 1,
            pageSize: 10,
            total: 0
        },
        // 表单验证规则
        rules: {
            username: [
                { required: true, message: '请输入用户名', trigger: 'blur' }
            ],
            phone: [
                { required: true, message: '请输入手机号', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
            ],
            password: [
                { required: true, message: '请输入密码', trigger: 'blur' }
            ]
        },
        // 产品列表
        productList: [],
        // 分类列表
        categoryList: [],
        // 编辑产品表单数据
        editProductForm: {
            name: '',
            description: '',
            price: 0,
            stock: 0,
            imageUrl: '',
            isNew: false,
            categoryIds: []
        },
        // 编辑产品对话框显示状态
        editProductDialogVisible: false,

        // 新增用户数据
        newUser: {
            userId: '',
            username: '',
            phone: '',
            password: '',
            registerTime: ''
        },
        addUserInfo: {
            username: '',
            password: '',
            email: '',
            phone: '',
            role: '',
            registerTime: null, // 确保registerTime属性存在
        },
        // 新增产品数据
        newProduct: {
            name: '',
            description: '',
            price: 0,
            stock: 0,
            imageUrl: '',
            isNew: false,
            categoryIds: []
        },
        // 新增分类数据
        newCategory: {
            name: ''
        },
        // 新增用户对话框显示状态
        addUserDialogVisible: false,
        // 新增产品对话框显示状态
        addProductDialogVisible: false,
        // 新增分类对话框显示状态
        addCategoryDialogVisible: false,
        // 编辑分类对话框显示状态
        editCategoryDialogVisible: false,
        editCategoryForm: {
            name: ''
        },
        // 销售图表实例
        salesChart: null,
        // 分类图表实例
        categoryChart: null,
        // 新增属性，初始显示所有面板
        activePanel: 'all',
        // 用户详情对话框显示状态
        userDetailDialogVisible: false,
        // 当前查看的用户详情
        currentUserDetail: null,
        // 编辑用户表单数据
        editForm: {
            userId: '',
            username: '',
            phone: '',
            password: '',
            registerTime: ''
        },
        // 控制密码显示/隐藏
        showPassword: false,

        // 多图片管理相关数据
        newProductImages: [],        // 新增产品的图片数组
        editProductImages: [],       // 编辑产品的图片数组

        // 轮播图管理相关数据
        carouselList: [],            // 轮播图列表
        addCarouselDialogVisible: false,  // 新增轮播图对话框显示状态
        editCarouselDialogVisible: false, // 编辑轮播图对话框显示状态
        newCarousel: {               // 新增轮播图数据
            title: '',
            description: '',
            imageUrl: '',
            categoryId: null,
            sortOrder: 0
        },
        editCarouselForm: {          // 编辑轮播图表单数据
            id: null,
            title: '',
            description: '',
            imageUrl: '',
            categoryId: null,
            sortOrder: 0
        },

        // 订单管理相关数据
        orderList: [],               // 订单列表
        orderLoading: false,         // 订单加载状态
        orderCurrentPage: 1,         // 订单当前页
        orderPageSize: 10,           // 订单每页大小
        orderTotal: 0,               // 订单总数
        orderStatusFilter: '',       // 订单状态筛选
        orderStats: {                // 订单统计
            totalOrders: 0,
            pendingOrders: 0,
            completedOrders: 0,
            totalAmount: 0
        },
        orderDetailDialogVisible: false,  // 订单详情对话框显示状态
        currentOrderDetail: null,    // 当前查看的订单详情

        // 数据分析相关数据
        analysisData: {              // 分析数据
            todaySales: '0',
            monthlySales: '0',
            yearlySales: '0',
            todayOrders: 0,
            monthlyOrders: 0,
            newUsers: 0,
            activeUsers: 0
        }
    },
    computed: {
        // 格式化编辑表单中的注册时间
        formattedRegisterTime() {
            if (!this.editForm.registerTime) return '';
            return this.formatDateTime(this.editForm.registerTime);
        },
        // 格式化新用户表单中的注册时间
        formattedNewUserRegisterTime() {
            if (!this.newUser.registerTime) return '';
            return this.formatDateTime(this.newUser.registerTime);
        }
    },
    created() {
        this.loadUserList();
        this.loadProductList();
        this.loadCategoryList();
        this.loadOrders();
        this.loadOrderStats();
        this.loadAnalysisData();
    },
    mounted() {
        // 设置当前日期
        this.setCurrentDate();
        // 加载数据
        this.loadData();
        // 添加点击页面其他区域关闭下拉菜单的事件
        document.addEventListener('click', this.handleDocumentClick);
    },
    beforeDestroy() {
        // 移除事件监听器，防止内存泄漏
        document.removeEventListener('click', this.handleDocumentClick);
    },
    methods: {
        // 切换下拉菜单显示状态
        toggleDropdown() {
            this.dropdownVisible = !this.dropdownVisible;
        },
        // 关闭下拉菜单
        closeDropdown() {
            this.dropdownVisible = false;
        },
        // 处理文档点击事件
        handleDocumentClick(event) {
            // 如果点击的元素不是下拉菜单的一部分，则关闭下拉菜单
            const dropdownElement = document.querySelector('.dropdown-content');
            const triggerElement = document.querySelector('.user-menu > div');

            if (dropdownElement && triggerElement) {
                if (!dropdownElement.contains(event.target) && !triggerElement.contains(event.target)) {
                    this.dropdownVisible = false;
                }
            }
        },
        // 切换激活的侧边栏项目
        changeTab(tab) {
            this.activeTab = tab;

            // 根据切换的tab加载对应的数据
            if (tab === 'dashboard') {
                // 仪表盘需要加载所有统计数据
                this.loadUserList();
                this.loadProductList();
                this.loadCategoryList();
            } else if (tab === 'userManagement') {
                this.loadUserList();
            } else if (tab === 'productManagement') {
                this.loadProductList();
            } else if (tab === 'categoryManagement') {
                this.loadCategoryList();
            } else if (tab === 'carouselManagement') {
                this.loadCarouselList();
            } else if (tab === 'orderManagement') {
                this.loadOrders();
                this.loadOrderStats();
            } else if (tab === 'dataAnalysis') {
                // 延迟初始化图表，确保DOM已渲染
                this.$nextTick(() => {
                    this.initCharts();
                    // 加载数据分析相关数据
                    this.loadAnalysisData();
                    this.loadOrderStats();
                });
            }

            // 触发重绘，确保样式更新
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
        },
        // 设置当前日期
        setCurrentDate() {
            const date = new Date();
            const currentDateElement = document.getElementById('current-date');
            if (currentDateElement) {
                currentDateElement.textContent = date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    weekday: 'long'
                });
            }
        },
        // 加载数据
        loadData() {
            this.loadUserList();
            this.loadProductList();
            this.loadCategoryList();
        },
        // 加载用户列表
        loadUserList() {
            const params = {
                page: this.userPagination.currentPage,
                size: this.userPagination.pageSize
            };
            axios.get('/user/userList/page', { params })
                .then(response => {
                    console.log('返回的用户分页数据:', response.data);
                    this.userList = response.data.data;
                    this.userPagination.total = response.data.total;
                    console.log('更新后的用户列表:', this.userList);
                    // 更新用户统计（仅在仪表盘页面存在时）
                    const totalUsersElement = document.getElementById('total-users');
                    if (totalUsersElement) {
                        totalUsersElement.textContent = response.data.total;
                    }
                    // 更新数据分析页面的用户统计
                    const totalUsersAnalysisElement = document.getElementById('total-users-analysis');
                    if (totalUsersAnalysisElement) {
                        totalUsersAnalysisElement.textContent = response.data.total;
                    }
                })
                .catch(error => {
                    console.error('获取用户列表出错:', error);
                    this.$message.error('获取用户列表失败');
                });
        },
        // 加载产品列表 - 使用强制刷新接口
        loadProductList() {
            const params = {
                page: this.productPagination.currentPage,
                size: this.productPagination.pageSize
            };

            // 使用强制刷新接口获取最新数据
            axios.get('/products/page/refresh', { params })
                .then(response => {
                    console.log('🔄 强制刷新产品数据响应:', response.data);
                    console.log('📋 产品列表数据:', response.data.data);

                    this.productList = response.data.data;
                    this.productPagination.total = response.data.total;

                    // 调试：检查每个产品的imageUrl
                    if (this.productList && this.productList.length > 0) {
                        console.log('🖼️ 产品图片URL检查:');
                        this.productList.forEach((product, index) => {
                            console.log(`产品${index + 1}:`, {
                                id: product.id,
                                name: product.name,
                                imageUrl: product.imageUrl,
                                hasImageUrl: !!product.imageUrl,
                                imageUrlLength: product.imageUrl ? product.imageUrl.length : 0
                            });
                        });
                    }

                    // 更新产品统计（仅在仪表盘页面存在时）
                    const totalProductsElement = document.getElementById('total-products');
                    if (totalProductsElement) {
                        totalProductsElement.textContent = response.data.total;
                    }
                    // 更新图表数据（仅在数据分析页面存在时）
                    if (this.salesChart) {
                        this.updateSalesChart();
                    }
                })
                .catch(error => {
                    console.error('获取产品列表出错:', error);
                    this.$message.error('获取产品列表失败');
                });
        },
        // 加载分类列表
        loadCategoryList() {
            const params = {
                page: this.categoryPagination.currentPage,
                size: this.categoryPagination.pageSize
            };
            axios.get('/admin/category/page', { params })
                .then(response => {
                    this.categoryList = response.data.data;
                    this.categoryPagination.total = response.data.total;
                })
                .catch(error => {
                    console.error('获取分类列表出错:', error);
                    this.$message.error('获取分类列表失败');
                });
        },
        // 保存用户信息（新增或更新）
        saveUser() {
            this.$refs.formRef.validate(valid => {
                if (valid) {
                    // 根据是否有userId判断是新增还是更新
                    const url = this.formData.userId ? '/user/updateUser' : '/user/addUser';
                    axios.post(url, this.formData)
                        .then(response => {
                            if (response.data.success) {
                                this.$message.success(this.formData.userId ? '更新成功' : '添加成功');
                                this.dialogVisible = false;
                                this.loadUserList(); // 刷新列表
                            } else {
                                this.$message.error(response.data.message || '操作失败');
                            }
                        })
                        .catch(error => {
                            console.error('操作失败:', error);
                            this.$message.error('操作失败');
                        });
                } else {
                    return false;
                }
            });
        },
        // 获取某分类下的产品数量
        getProductCountByCategory(categoryId) {
            // 这里简化处理，实际项目中应通过API获取
            return Math.floor(Math.random() * 10) + 1;
        },
        // 初始化图表
        initCharts() {
            // 检查图表元素是否存在
            const salesChartElement = document.getElementById('salesChart');
            const categoryChartElement = document.getElementById('categoryChart');

            if (!salesChartElement || !categoryChartElement) {
                console.log('图表元素未找到，跳过初始化');
                return;
            }

            // 如果图表已存在，先销毁
            if (this.salesChart) {
                this.salesChart.destroy();
            }
            if (this.categoryChart) {
                this.categoryChart.destroy();
            }

            // 销售趋势图表
            const salesCtx = salesChartElement.getContext('2d');
            this.salesChart = new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额',
                        data: [12000, 19000, 16000, 21000, 25000, 30000],
                        borderColor: '#D81B60',
                        backgroundColor: 'rgba(216, 27, 96, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value;
                                }
                            }
                        }
                    }
                }
            });
            // 产品分类分布图表
            const categoryCtx = categoryChartElement.getContext('2d');
            this.categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['护肤品', '彩妆', '香水', '美发', '身体护理'],
                    datasets: [{
                        data: [35, 25, 15, 15, 10],
                        backgroundColor: [
                            '#D81B60',
                            '#8E24AA',
                            '#FF9800',
                            '#4CAF50',
                            '#2196F3'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    },
                    cutout: '65%'
                }
            });
        },
        // 更新销售图表
        updateSalesChart() {
            // 这里简化处理，实际项目中应通过API获取真实数据
            const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
            const salesData = months.map(() => Math.floor(Math.random() * 30000) + 10000);

            if (this.salesChart) {
                this.salesChart.data.datasets[0].data = salesData;
                this.salesChart.update();
            }
            // 更新月度销售额统计（仅在仪表盘页面存在时）
            const monthlySalesElement = document.getElementById('monthly-sales');
            if (monthlySalesElement) {
                monthlySalesElement.textContent = '¥' + salesData[salesData.length - 1].toLocaleString();
            }
        },
        // 用户分页处理方法
        handleUserSizeChange(size) {
            this.userPagination.pageSize = size;
            this.userPagination.currentPage = 1; // 重置到第一页
            this.loadUserList();
        },
        handleUserCurrentChange(page) {
            this.userPagination.currentPage = page;
            this.loadUserList();
        },
        // 产品分页处理方法
        handleProductSizeChange(size) {
            this.productPagination.pageSize = size;
            this.productPagination.currentPage = 1; // 重置到第一页
            this.loadProductList();
        },
        handleProductCurrentChange(page) {
            this.productPagination.currentPage = page;
            this.loadProductList();
        },
        // 分类分页处理方法
        handleCategorySizeChange(size) {
            this.categoryPagination.pageSize = size;
            this.categoryPagination.currentPage = 1; // 重置到第一页
            this.loadCategoryList();
        },
        handleCategoryCurrentChange(page) {
            this.categoryPagination.currentPage = page;
            this.loadCategoryList();
        },
        // 打开新增用户对话框
        openAddUserDialog() {
            // 清空表单数据
            this.newUser = {
                userId: '',
                username: '',
                phone: '',
                password: '',
                registerTime: new Date().getTime() // 设置当前时间戳
            };
            // 显示新增对话框
            this.addUserDialogVisible = true;
        },
        openAddProductDialog() {
            this.addProductDialogVisible = true;
            this.newProduct = {
                name: '',
                description: '',
                price: 0,
                stock: 0,
                imageUrl: '',
                isNew: false,
                categoryIds: []
            };
            // 清空图片数组
            this.newProductImages = [];
        },
        openAddCategoryDialog() {
            this.addCategoryDialogVisible = true;
            this.newCategory = {
                name: ''
            };
        },
        // 新增用户
        addUser() {
            this.$refs.userForm.validate(valid => {
                if (valid) {
                    // 设置当前时间作为注册时间
                    this.newUser.registerTime = new Date().getTime();

                    console.log('请求数据:', this.newUser); // 调试输出
                    axios.post('/user/register', this.newUser)
                        .then(response => {
                            if (response.data.success) {
                                this.$message.success('新增用户成功');
                                this.addUserDialogVisible = false;
                                this.loadUserList();
                                this.resetUserForm();
                            } else {
                                this.$message.error(response.data.message);
                            }
                        })
                        .catch(error => {
                            console.error('新增用户出错:', error);
                            this.$message.error('新增用户失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },
        // 编辑用户
        editUser(user) {
            // 显示编辑对话框
            this.dialogVisible = true;
            // 重置密码显示状态
            this.showPassword = false;
            // 将选中用户的信息复制到编辑表单中
            this.editForm = {
                userId: user.userId,
                username: user.username,
                phone: user.phone,
                password: user.password || '', // 如果密码为空，设置为空字符串
                registerTime: user.registerTime
            };
        },
        // 提交表单（添加/编辑）
        // 保存用户信息的方法
        saveUser() {
            // 使用 Axios 发送 POST 请求到后端的更新用户接口
            axios.post('/user/updateUser', this.editForm)
                .then(response => {
                    if (response.data.success) {
                        // 如果更新成功，更新用户列表中的该用户信息
                        const index = this.userList.findIndex(u => u.userId === this.editForm.userId);
                        if (index !== -1) {
                            this.userList.splice(index, 1, this.editForm);
                        }
                        // 关闭编辑对话框
                        this.dialogVisible = false;
                        // 显示成功提示信息
                        this.$message.success('用户信息更新成功');
                    } else {
                        // 如果更新失败，显示错误提示信息
                        this.$message.error('用户信息更新失败');
                    }
                })
                .catch(error => {
                    // 如果请求出错，显示错误提示信息
                    this.$message.error('更新用户信息时出现错误');
                    console.error(error);
                });
        },
        // 删除用户
        deleteUser(userId) {
            // 使用 Axios 发送 DELETE 请求到后端的删除用户接口
            axios.delete(`/user/deleteUser/${userId}`)
                .then(response => {
                    if (response.data.success) {
                        // 如果删除成功，从用户列表中移除该用户
                        this.userList = this.userList.filter(user => user.userId !== userId);
                        // 显示成功提示信息
                        this.$message.success('用户删除成功');
                    } else {
                        // 如果删除失败，显示错误提示信息
                        this.$message.error(response.data.message);
                    }
                })
                .catch(error => {
                    // 如果请求出错，显示错误提示信息
                    this.$message.error('删除用户时出现错误');
                    console.error(error);
                });
        },
        // 新增产品
        addProduct() {
            this.$refs.productForm.validate((valid) => {
                if (valid) {
                    console.log('🔄 正在新增产品:', this.newProduct);
                    console.log('📸 产品图片数量:', this.newProductImages.length);

                    // 第一步：创建产品
                    axios.post('/products/add', this.newProduct)
                        .then(response => {
                            console.log('📋 产品新增响应:', response.data);

                            // 检查新的JSON响应格式
                            if (response.data && response.data.success) {
                                const productId = response.data.productId;
                                console.log('✅ 产品创建成功，ID:', productId);

                                // 第二步：如果有图片，上传图片
                                if (this.newProductImages.length > 0 && productId) {
                                    this.uploadNewProductImages(productId);
                                } else {
                                    // 没有图片，直接完成
                                    this.$message.success(response.data.message || '产品新增成功');
                                    this.addProductDialogVisible = false;
                                    this.loadProductList();
                                    this.resetProductForm();
                                }
                            } else {
                                const errorMsg = response.data && response.data.message ? response.data.message : '产品新增失败';
                                this.$message.error(errorMsg);
                                console.error('❌ 产品新增失败:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('❌ 新增产品出错:', error);
                            this.$message.error('产品新增失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },

        // 上传新增产品的图片
        uploadNewProductImages(productId) {
            console.log('🔄 开始上传产品图片，产品ID:', productId);

            const loading = this.$loading({
                lock: true,
                text: `正在上传 ${this.newProductImages.length} 张图片...`,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });

            let uploadedCount = 0;
            let failedCount = 0;
            const totalImages = this.newProductImages.length;

            // 逐个上传图片
            const uploadPromises = this.newProductImages.map((imageItem, index) => {
                const formData = new FormData();
                formData.append('file', imageItem.file);
                formData.append('productId', productId);
                formData.append('imageName', imageItem.imageName);
                formData.append('isPrimary', imageItem.isPrimary ? '1' : '0');
                formData.append('sortOrder', index.toString());

                return axios.post('/products/upload-image', formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    }
                }).then(response => {
                    if (response.data.success) {
                        uploadedCount++;
                        console.log(`✅ 图片 ${imageItem.imageName} 上传成功`);
                    } else {
                        failedCount++;
                        console.error(`❌ 图片 ${imageItem.imageName} 上传失败:`, response.data.message);
                    }
                }).catch(error => {
                    failedCount++;
                    console.error(`❌ 图片 ${imageItem.imageName} 上传出错:`, error);
                });
            });

            // 等待所有图片上传完成
            Promise.allSettled(uploadPromises).then(() => {
                loading.close();

                if (uploadedCount > 0) {
                    this.$message.success(`产品创建成功！成功上传 ${uploadedCount} 张图片`);

                    if (failedCount > 0) {
                        this.$message.warning(`有 ${failedCount} 张图片上传失败，您可以稍后在编辑页面重新上传`);
                    }
                } else {
                    this.$message.warning('产品创建成功，但图片上传失败，您可以稍后在编辑页面上传图片');
                }

                // 完成操作
                this.addProductDialogVisible = false;
                this.loadProductList();
                this.resetProductForm();
            });
        },
        // 编辑产品的方法
        editProduct(product) {
            console.log('🔄 正在编辑产品:', product);

            // 显示编辑产品对话框
            this.editProductDialogVisible = true;
            // 将选中产品的信息复制到编辑产品表单中
            this.editProductForm = {
                id: product.id, // 添加 id 字段
                name: product.name,
                description: product.description,
                price: product.price,
                stock: product.stock || 0,
                imageUrl: product.imageUrl,
                isNew: product.isNew,
                categoryIds: [] // 先设为空数组，稍后异步加载
            };

            // 异步加载产品的分类信息
            this.loadProductCategories(product.id);

            // 清空之前的图片数据
            this.editProductImages = [];

            // 加载产品图片
            this.$nextTick(() => {
                this.loadEditProductImages();
            });

            // 确保表单字段重置后仍能显示数据
            if (this.$refs.editProductForm) {
                this.$refs.editProductForm.resetFields();
                this.$nextTick(() => {
                    Object.keys(this.editProductForm).forEach(key => {
                        if (this.$refs.editProductForm.fields[key]) {
                            this.$refs.editProductForm.fields[key].validate();
                        }
                    });
                });
            }
        },

        // 加载产品的分类信息
        loadProductCategories(productId) {
            console.log('🔄 正在加载产品分类信息:', productId);

            axios.get(`/products/${productId}/categories`)
                .then(response => {
                    console.log('📋 产品分类响应:', response.data);

                    if (response.data && response.data.success) {
                        this.editProductForm.categoryIds = response.data.categoryIds || [];
                        console.log('✅ 成功加载产品分类:', this.editProductForm.categoryIds);
                    } else {
                        console.warn('⚠️ 加载产品分类失败:', response.data);
                        this.editProductForm.categoryIds = [];
                    }
                })
                .catch(error => {
                    console.error('❌ 加载产品分类出错:', error);
                    this.editProductForm.categoryIds = [];
                });
        },

        // 保存产品编辑信息（新增或更新）
        saveProduct() {
            this.$refs.editProductForm.validate((valid) => {
                if (valid) {
                    console.log('🔄 正在更新产品:', this.editProductForm);

                    axios.post('/products/updateProduct', this.editProductForm)
                        .then(response => {
                            console.log('📋 产品更新响应:', response.data);

                            // 检查新的JSON响应格式
                            if (response.data && response.data.success) {
                                this.$message.success(response.data.message || '产品更新成功');
                                this.editProductDialogVisible = false;
                                this.loadProductList();
                            } else {
                                const errorMsg = response.data && response.data.message ? response.data.message : '产品更新失败';
                                this.$message.error(errorMsg);
                                console.error('❌ 产品更新失败:', response.data);
                            }
                        })
                        .catch(error => {
                            console.error('❌ 更新产品出错:', error);
                            this.$message.error('产品更新失败，请稍后重试');
                        });
                } else {
                    this.$message.warning('请检查表单数据');
                    return false;
                }
            });
        },
        // 删除产品
        deleteProduct(id) {
            this.$confirm('确定要删除该产品吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/products/${id}`)
                    .then(response => {
                        if (response.data === '删除成功') {
                            this.$message.success('产品删除成功');
                            this.loadProductList(); // 刷新产品列表
                        } else {
                            this.$message.error('产品删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除产品出错:', error);
                        this.$message.error('产品删除失败，请稍后重试');
                    });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 新增分类
        addCategory() {
            this.$refs.categoryForm.validate(valid => {
                if (valid) {
                    axios.post('/admin/category/add', this.newCategory)
                        .then(response => {
                            if (response) {
                                this.$message.success('新增分类成功');
                                this.addCategoryDialogVisible = false;
                                this.loadCategoryList();
                                this.resetCategoryForm();
                            } else {
                                this.$message.error('新增分类失败');
                            }
                        })
                        .catch(error => {
                            console.error('新增分类出错:', error);
                            this.$message.error('新增分类失败');
                        });
                } else {
                    return false;
                }
            });
        },
        // 重置分类表单
        resetCategoryForm() {
            this.newCategory = {
                name: ''
            };
            if (this.$refs.categoryForm) {
                this.$refs.categoryForm.resetFields();
            }
        },
        // 编辑分类
        editCategory(category) {
            // 这里可以实现编辑分类的逻辑
            // 显示编辑分类对话框
            this.editCategoryDialogVisible = true;
            // 将选中分类的信息复制到编辑分类表单中
            this.editCategoryForm = { ...category };
            // 确保表单字段重置后仍能显示数据
            if (this.$refs.editCategoryForm) {
                this.$refs.editCategoryForm.resetFields();
                this.$nextTick(() => {
                    Object.keys(this.editCategoryForm).forEach(key => {
                        if (this.$refs.editCategoryForm.fields[key]) {
                            this.$refs.editCategoryForm.fields[key].validate();
                        }
                    });
                });
            }
        },
        // 保存分类编辑信息
        saveCategory() {
            let url = '';
            let method = '';
            if (this.editCategoryDialogVisible) {
                // 编辑分类
                url = '/admin/category/update';
                method = 'put';
            } else {
                // 新增分类
                url = '/admin/category/add';
                method = 'post';
            }

            axios({
                method: method,
                url: url,
                data: this.editCategoryDialogVisible ? this.editCategoryForm : this.newCategory
            })
                .then(response => {
                    if (response.data) {
                        if (this.editCategoryDialogVisible) {
                            this.$message.success('分类信息更新成功');
                        } else {
                            this.$message.success('新增分类成功');
                        }
                        // 关闭对话框
                        this.editCategoryDialogVisible = false;
                        this.addCategoryDialogVisible = false;
                        // 重新加载分类列表
                        this.loadCategoryList();
                    } else {
                        if (this.editCategoryDialogVisible) {
                            this.$message.error('分类信息更新失败');
                        } else {
                            this.$message.error('新增分类失败');
                        }
                    }
                })
                .catch(error => {
                    console.error('保存分类信息出错:', error);
                    if (this.editCategoryDialogVisible) {
                        this.$message.error('分类信息更新失败');
                    } else {
                        this.$message.error('新增分类失败');
                    }
                });
        },
        // 删除分类
        deleteCategory(categoryId) {
            this.$confirm('确定要删除该分类吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 确保 categoryId 是有效的 ID
                axios.delete(`/admin/category/delete/${categoryId}`)
                    .then(response => {
                        if (response.data) {
                            this.$message.success('删除分类成功');
                            this.loadCategoryList(); // 重新加载分类列表
                        } else {
                            this.$message.error('删除分类失败');
                        }
                    })
                    .catch(error => {
                        console.error('删除分类出错:', error);
                        this.$message.error('删除分类失败');
                    });
            }).catch(() => {
                // 用户取消操作
            });
        },
        // 处理文件上传成功后的回调
        handleUploadSuccess(response, file, fileList) {
            if (response.success) {
                if (this.addProductDialogVisible) {
                    this.newProduct.imageUrl = response.imageUrl;
                } else if (this.editProductDialogVisible) {
                    this.editProductForm.imageUrl = response.imageUrl;
                }
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message);
            }
        },
        // 处理新增产品图片上传成功
        handleAddProductUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.newProduct.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },
        // 处理文件上传前的钩子，限制上传文件的类型
        beforeUpload(file) {
            const isImage = ['image/jpeg', 'image/png'].includes(file.type);
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isImage) {
                this.$message.error('上传图片只能是 JPG 或 PNG 格式!');
                return false;
            }
            if (!isLt10M) {
                this.$message.error('上传图片大小不能超过 10MB!');
                return false;
            }
            return true;
        },
        // 重置用户表单
        resetUserForm() {
            this.newUser = {
                userId: '',
                username: '',
                phone: '',
                password: '',
                registerTime: new Date().getTime()
            };
            this.$refs.userForm.resetFields();
        },
        // 重置产品表单
        resetProductForm() {
            this.newProduct = {
                name: '',
                description: '',
                price: 0,
                stock: 0,
                imageUrl: '',
                isNew: false,
                categoryIds: []
            };
            // 清空图片数组
            this.newProductImages = [];

            if (this.$refs.productForm) {
                this.$refs.productForm.resetFields();
            }
        },
        // 重置分类表单
        resetCategoryForm() {
            this.newCategory = {
                name: ''
            };
            this.$refs.categoryForm.resetFields();
        },
        // 格式化日期
        formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        },
        // 格式化日期时间（包含时分秒）
        formatDateTime(timestamp) {
            if (!timestamp) return '-';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        },
        // 查看用户详情
        viewUserDetail(user) {
            console.log('查看用户详情:', user);

            // 显示加载状态
            this.currentUserDetail = null;
            this.userDetailDialogVisible = true;

            // 调用API获取用户详情
            axios.get(`/api/user-detail/${user.phone}`)
                .then(response => {
                    console.log('用户详情响应:', response.data);
                    if (response.data.success) {
                        this.currentUserDetail = response.data.data;
                    } else {
                        this.$message.error(response.data.message || '获取用户详情失败');
                        this.userDetailDialogVisible = false;
                    }
                })
                .catch(error => {
                    console.error('获取用户详情失败:', error);
                    this.$message.error('获取用户详情失败');
                    this.userDetailDialogVisible = false;
                });
        },
        // 处理头像加载错误
        handleAvatarError(event) {
            // 使用base64编码的默认头像，避免网络依赖
            event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iMzAiIGZpbGw9IiNjY2NjY2MiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeD0iMTgiIHk9IjE4Ij4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjNjY2NjY2Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM2NjY2NjYiLz4KPC9zdmc+Cjwvc3ZnPg==';
        },

        // ==================== 多图片管理相关方法 ====================

        // 新增产品 - 处理图片选择（支持多张图片）
        handleNewProductImageChange(file, fileList) {
            // 处理单个文件
            const processFile = (fileItem) => {
                const isJPGorPNG = fileItem.raw.type === 'image/jpeg' || fileItem.raw.type === 'image/png';
                const isLt10M = fileItem.raw.size / 1024 / 1024 < 10;

                if (!isJPGorPNG) {
                    this.$message.error(`文件 ${fileItem.name} 格式不支持，只能上传 JPG/PNG 格式的图片!`);
                    return false;
                }
                if (!isLt10M) {
                    this.$message.error(`文件 ${fileItem.name} 大小超过限制，单张图片不能超过 10MB!`);
                    return false;
                }

                // 创建预览URL
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.newProductImages.push({
                        file: fileItem.raw,
                        imageUrl: e.target.result,
                        imageName: fileItem.name,
                        isPrimary: this.newProductImages.length === 0 // 第一张设为主图
                    });

                    // 显示成功消息
                    this.$message.success(`图片 ${fileItem.name} 添加成功`);
                };
                reader.readAsDataURL(fileItem.raw);
                return true;
            };

            // 如果是多个文件，逐个处理
            if (fileList && fileList.length > 1) {
                let successCount = 0;
                fileList.forEach(fileItem => {
                    if (processFile(fileItem)) {
                        successCount++;
                    }
                });
                if (successCount > 0) {
                    this.$message.success(`成功添加 ${successCount} 张图片，第一张将作为产品封面`);
                }
            } else {
                // 单个文件处理
                processFile(file);
            }

            return false; // 阻止自动上传
        },

        // 新增产品 - 删除图片
        deleteNewProductImage(index) {
            const deletedImage = this.newProductImages[index];
            this.newProductImages.splice(index, 1);

            this.$message.success(`图片 ${deletedImage.imageName} 已删除`);

            // 如果删除的是第一张且还有其他图片，重新设置第一张为主图
            if (index === 0 && this.newProductImages.length > 0) {
                this.newProductImages[0].isPrimary = true;
                this.$message.info('已将第一张图片设为新的封面图');
            }
        },

        // 编辑产品 - 加载产品图片
        loadEditProductImages() {
            if (!this.editProductForm.id) return;

            axios.get(`/products/${this.editProductForm.id}/images`)
                .then(response => {
                    if (response.data.success) {
                        this.editProductImages = response.data.data || [];
                        this.$message.success('图片列表刷新成功');
                    } else {
                        this.$message.error('获取图片失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('加载图片失败:', error);
                    this.$message.error('加载图片失败');
                });
        },

        // 编辑产品 - 删除图片（带确认和动画效果）
        deleteEditProductImage(imageId) {
            // 找到要删除的图片信息
            const imageToDelete = this.editProductImages.find(img => img.id === imageId);
            const imageName = imageToDelete ? imageToDelete.imageName || '图片' : '图片';

            this.$confirm(`确定要删除图片 "${imageName}" 吗？删除后无法恢复。`, '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                center: true
            }).then(() => {
                // 显示删除中的加载状态
                const loading = this.$loading({
                    lock: true,
                    text: '正在删除图片...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                axios.delete(`/api/product-images/${imageId}`)
                    .then(response => {
                        loading.close();
                        if (response.data.success) {
                            this.$message({
                                message: `图片 "${imageName}" 删除成功`,
                                type: 'success',
                                duration: 3000
                            });
                            this.loadEditProductImages(); // 重新加载图片列表
                        } else {
                            this.$message.error('删除失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        loading.close();
                        console.error('删除失败:', error);
                        this.$message.error('删除失败，请稍后重试');
                    });
            }).catch(() => {
                this.$message({
                    message: '已取消删除操作',
                    type: 'info',
                    duration: 2000
                });
            });
        },

        // 编辑产品 - 设置主图（带确认和动画效果）
        setEditProductPrimaryImage(imageId) {
            // 找到要设置为主图的图片信息
            const imageToSetPrimary = this.editProductImages.find(img => img.id === imageId);
            const imageName = imageToSetPrimary ? imageToSetPrimary.imageName || '图片' : '图片';

            this.$confirm(`确定要将图片 "${imageName}" 设为产品封面吗？`, '设置封面确认', {
                confirmButtonText: '确定设置',
                cancelButtonText: '取消',
                type: 'info',
                center: true
            }).then(() => {
                // 显示设置中的加载状态
                const loading = this.$loading({
                    lock: true,
                    text: '正在设置封面...',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                // 获取图片URL
                const imageUrl = imageToSetPrimary ? imageToSetPrimary.imageUrl : null;

                if (!imageUrl) {
                    loading.close();
                    this.$message.error('无法获取图片URL');
                    return;
                }

                console.log('🔄 设置主图:', {
                    productId: this.editProductForm.id,
                    imageUrl: imageUrl,
                    imageName: imageName
                });

                axios.post(`/products/${this.editProductForm.id}/set-primary-image`, {
                    imageUrl: imageUrl
                })
                    .then(response => {
                        loading.close();
                        console.log('📋 设置主图响应:', response.data);

                        if (response.data.success) {
                            this.$message({
                                message: `图片 "${imageName}" 已设为产品封面`,
                                type: 'success',
                                duration: 3000
                            });
                            this.loadEditProductImages(); // 重新加载图片列表
                            this.loadProductList(); // 重新加载产品列表以更新封面显示
                        } else {
                            this.$message.error('设置封面失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        loading.close();
                        console.error('设置封面失败:', error);
                        this.$message.error('设置封面失败，请稍后重试');
                    });
            }).catch(() => {
                this.$message({
                    message: '已取消设置封面操作',
                    type: 'info',
                    duration: 2000
                });
            });
        },

        // 编辑产品 - 多图片上传成功回调
        handleEditProductMultiUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.$message({
                    message: response.message || `图片 "${file.name}" 上传成功`,
                    type: 'success',
                    duration: 3000
                });

                // 显示上传详情
                if (response.uploadedCount) {
                    this.$notify({
                        title: '上传完成',
                        message: `成功上传 ${response.uploadedCount} 张图片`,
                        type: 'success',
                        duration: 4000
                    });
                }

                // 重新加载图片列表
                setTimeout(() => {
                    this.loadEditProductImages();
                }, 500);
            } else {
                this.$message.error(response.message || `图片 "${file.name}" 上传失败`);
            }
        },

        // 编辑产品 - 多图片上传失败回调
        handleEditProductMultiUploadError(err, file, fileList) {
            console.error('上传失败:', err);
            this.$message({
                message: `图片 "${file.name}" 上传失败，请检查网络连接后重试`,
                type: 'error',
                duration: 4000
            });
        },

        // 编辑产品 - 上传前验证
        beforeEditProductMultiUpload(file) {
            const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPGorPNG) {
                this.$message({
                    message: `文件 "${file.name}" 格式不支持，只能上传 JPG/PNG 格式的图片!`,
                    type: 'error',
                    duration: 4000
                });
                return false;
            }
            if (!isLt10M) {
                this.$message({
                    message: `文件 "${file.name}" 大小超过限制，单张图片不能超过 10MB!`,
                    type: 'error',
                    duration: 4000
                });
                return false;
            }

            // 显示上传开始提示
            this.$message({
                message: `开始上传图片 "${file.name}"...`,
                type: 'info',
                duration: 2000
            });

            return true;
        },

        // 处理图片加载错误
        handleImageError(event) {
            console.log('图片加载失败:', event.target.src);
            // 使用实际存在的默认图片
            event.target.src = '/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg';
        },

        // 处理图片加载成功
        handleImageLoad(event) {
            console.log('图片加载成功:', event.target.src);
        },

        // ==================== UI 交互方法 ====================

        // 处理用户菜单悬停效果
        handleUserMenuHover(event) {
            if (event && event.target) {
                event.target.classList.add('user-menu-hover');
            }
        },

        // 处理用户菜单离开效果
        handleUserMenuLeave(event) {
            if (event && event.target) {
                event.target.classList.remove('user-menu-hover');
            }
        },

        // ==================== 轮播图管理相关方法 ====================

        // 加载轮播图列表
        loadCarouselList() {
            axios.get('/carousel/images')
                .then(response => {
                    console.log('📋 轮播图列表响应:', response.data);
                    if (response.data.success) {
                        this.carouselList = response.data.data || [];
                        this.$message.success('轮播图列表加载成功');
                    } else {
                        this.$message.error('获取轮播图列表失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('获取轮播图列表失败:', error);
                    this.$message.error('获取轮播图列表失败');
                });
        },

        // 新增轮播图
        addCarousel() {
            if (!this.newCarousel.title) {
                this.$message.error('请输入轮播图标题');
                return;
            }
            if (!this.newCarousel.imageUrl) {
                this.$message.error('请上传轮播图片');
                return;
            }

            const carouselData = {
                name: '轮播图',
                title: this.newCarousel.title,
                description: this.newCarousel.description,
                imageUrl: this.newCarousel.imageUrl,
                categoryId: this.newCarousel.categoryId,
                sortOrder: this.newCarousel.sortOrder || 0
            };

            axios.post('/carousel/add', carouselData)
                .then(response => {
                    console.log('📋 新增轮播图响应:', response.data);
                    if (response.data.success) {
                        this.$message.success('轮播图添加成功');
                        this.addCarouselDialogVisible = false;
                        this.resetNewCarouselForm();
                        this.loadCarouselList();
                    } else {
                        this.$message.error('添加轮播图失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('添加轮播图失败:', error);
                    this.$message.error('添加轮播图失败');
                });
        },

        // 编辑轮播图
        editCarousel(carousel) {
            this.editCarouselForm = {
                id: carousel.id,
                title: carousel.title,
                description: carousel.description,
                imageUrl: carousel.image_url || carousel.imageUrl,
                categoryId: carousel.category_id || carousel.categoryId,
                sortOrder: carousel.sort_order || carousel.sortOrder || 0
            };
            this.editCarouselDialogVisible = true;
        },

        // 保存轮播图修改
        saveCarousel() {
            if (!this.editCarouselForm.title) {
                this.$message.error('请输入轮播图标题');
                return;
            }
            if (!this.editCarouselForm.imageUrl) {
                this.$message.error('请上传轮播图片');
                return;
            }

            const carouselData = {
                id: this.editCarouselForm.id,
                name: '轮播图',
                title: this.editCarouselForm.title,
                description: this.editCarouselForm.description,
                imageUrl: this.editCarouselForm.imageUrl,
                categoryId: this.editCarouselForm.categoryId,
                sortOrder: this.editCarouselForm.sortOrder || 0
            };

            axios.put('/carousel/update', carouselData)
                .then(response => {
                    console.log('📋 更新轮播图响应:', response.data);
                    if (response.data.success) {
                        this.$message.success('轮播图更新成功');
                        this.editCarouselDialogVisible = false;
                        this.loadCarouselList();
                    } else {
                        this.$message.error('更新轮播图失败: ' + response.data.message);
                    }
                })
                .catch(error => {
                    console.error('更新轮播图失败:', error);
                    this.$message.error('更新轮播图失败');
                });
        },

        // 删除轮播图
        deleteCarousel(carouselId) {
            this.$confirm('确定要删除这张轮播图吗？删除后无法恢复。', '删除确认', {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.delete(`/carousel/delete/${carouselId}`)
                    .then(response => {
                        console.log('📋 删除轮播图响应:', response.data);
                        if (response.data.success) {
                            this.$message.success('轮播图删除成功');
                            this.loadCarouselList();
                        } else {
                            this.$message.error('删除轮播图失败: ' + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('删除轮播图失败:', error);
                        this.$message.error('删除轮播图失败');
                    });
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 切换轮播图状态
        toggleCarouselStatus(carousel) {
            const newStatus = !carousel.is_active;
            const statusText = newStatus ? '启用' : '禁用';

            this.$confirm(`确定要${statusText}这张轮播图吗？`, '状态切换确认', {
                confirmButtonText: `确定${statusText}`,
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                axios.put(`/carousel/toggle-status/${carousel.id}`)
                    .then(response => {
                        console.log('📋 切换轮播图状态响应:', response.data);
                        if (response.data.success) {
                            this.$message.success(`轮播图${statusText}成功`);
                            this.loadCarouselList();
                        } else {
                            this.$message.error(`${statusText}轮播图失败: ` + response.data.message);
                        }
                    })
                    .catch(error => {
                        console.error(`${statusText}轮播图失败:`, error);
                        this.$message.error(`${statusText}轮播图失败`);
                    });
            }).catch(() => {
                this.$message.info('已取消操作');
            });
        },

        // 重置新增轮播图表单
        resetNewCarouselForm() {
            this.newCarousel = {
                title: '',
                description: '',
                imageUrl: '',
                categoryId: null,
                sortOrder: 0
            };
        },

        // 处理轮播图上传成功
        handleCarouselUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.newCarousel.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },

        // 处理编辑轮播图上传成功
        handleEditCarouselUploadSuccess(response, file, fileList) {
            if (response.success) {
                this.editCarouselForm.imageUrl = response.imageUrl;
                this.$message.success('图片上传成功');
            } else {
                this.$message.error(response.message || '图片上传失败');
            }
        },

        // 轮播图上传前验证
        beforeCarouselUpload(file) {
            const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 2;

            if (!isJPGorPNG) {
                this.$message.error('上传图片只能是 JPG/PNG 格式!');
            }
            if (!isLt2M) {
                this.$message.error('上传图片大小不能超过 2MB!');
            }
            return isJPGorPNG && isLt2M;
        },

        // ==================== 订单管理相关方法 ====================

        // 加载订单列表
        loadOrders() {
            this.orderLoading = true;
            const params = {
                page: this.orderCurrentPage,
                size: this.orderPageSize,
                status: this.orderStatusFilter || undefined
            };

            axios.get('/api/admin/orders', { params })
                .then(response => {
                    if (response.data.success) {
                        this.orderList = response.data.data;
                        this.orderTotal = response.data.total;
                    } else {
                        this.$message.error('获取订单列表失败');
                    }
                })
                .catch(error => {
                    console.error('获取订单列表失败:', error);
                    this.$message.error('网络错误，请重试');
                })
                .finally(() => {
                    this.orderLoading = false;
                });
        },

        // 加载订单统计
        loadOrderStats() {
            axios.get('/api/admin/orders/stats')
                .then(response => {
                    if (response.data.success) {
                        this.orderStats = response.data.data;
                    }
                })
                .catch(error => {
                    console.error('获取订单统计失败:', error);
                });
        },

        // 订单分页处理
        handleOrderSizeChange(size) {
            this.orderPageSize = size;
            this.orderCurrentPage = 1;
            this.loadOrders();
        },

        handleOrderCurrentChange(page) {
            this.orderCurrentPage = page;
            this.loadOrders();
        },

        // 查看订单详情
        viewOrderDetail(order) {
            // 获取订单完整信息包括商品详情
            axios.get(`/api/admin/orders/${order.orderNo}`)
                .then(response => {
                    if (response.data.success) {
                        this.currentOrderDetail = response.data.data;
                        this.orderDetailDialogVisible = true;
                    } else {
                        this.$message.error('获取订单详情失败');
                    }
                })
                .catch(error => {
                    console.error('获取订单详情失败:', error);
                    this.$message.error('网络错误，请重试');
                });
        },

        // 发货
        shipOrder(order) {
            this.$confirm('确定要将此订单标记为已发货吗？', '确认发货', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/orders/${order.orderNo}/ship`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('发货成功');
                            this.loadOrders();
                            this.loadOrderStats();
                        } else {
                            this.$message.error(response.data.message || '发货失败');
                        }
                    })
                    .catch(error => {
                        console.error('发货失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 管理员取消订单
        cancelOrderAdmin(order) {
            this.$confirm('确定要取消此订单吗？', '确认取消', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                axios.post(`/api/admin/orders/${order.orderNo}/cancel`)
                    .then(response => {
                        if (response.data.success) {
                            this.$message.success('订单已取消');
                            this.loadOrders();
                            this.loadOrderStats();
                        } else {
                            this.$message.error(response.data.message || '取消失败');
                        }
                    })
                    .catch(error => {
                        console.error('取消订单失败:', error);
                        this.$message.error('网络错误，请重试');
                    });
            });
        },

        // 获取订单状态文本
        getOrderStatusText(status) {
            const statusMap = {
                1: '待付款',
                2: '待发货',
                3: '待收货',
                4: '已完成',
                5: '已取消'
            };
            return statusMap[status] || '未知状态';
        },

        // 获取订单状态类型（用于标签颜色）
        getOrderStatusType(status) {
            const typeMap = {
                1: 'warning',
                2: 'primary',
                3: 'info',
                4: 'success',
                5: 'danger'
            };
            return typeMap[status] || 'info';
        },

        // 格式化时间戳
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            return new Date(timestamp).toLocaleString('zh-CN');
        },

        // ==================== 数据分析相关方法 ====================

        // 加载数据分析数据
        loadAnalysisData() {
            axios.get('/api/admin/analysis/data')
                .then(response => {
                    if (response.data.success) {
                        this.analysisData = response.data.data;
                        // 更新图表数据
                        this.updateChartsWithRealData();
                    }
                })
                .catch(error => {
                    console.error('获取分析数据失败:', error);
                    // 使用模拟数据
                    this.analysisData = {
                        todaySales: '12,345',
                        monthlySales: '456,789',
                        yearlySales: '2,345,678',
                        todayOrders: 23,
                        monthlyOrders: 567,
                        newUsers: 12,
                        activeUsers: 234
                    };
                });
        },

        // 使用真实数据更新图表
        updateChartsWithRealData() {
            if (this.salesChart && this.analysisData.salesTrend) {
                this.salesChart.data.datasets[0].data = this.analysisData.salesTrend.values || [];
                this.salesChart.data.labels = this.analysisData.salesTrend.labels || [];
                this.salesChart.update();
            }

            if (this.categoryChart && this.analysisData.categoryDistribution) {
                this.categoryChart.data.datasets[0].data = this.analysisData.categoryDistribution.values || [];
                this.categoryChart.data.labels = this.analysisData.categoryDistribution.labels || [];
                this.categoryChart.update();
            }
        },

        // 初始化图表时使用真实数据
        initChartsWithData() {
            this.$nextTick(() => {
                this.initSalesChart();
                this.initCategoryChart();
                // 加载真实数据并更新图表
                this.loadAnalysisData();
            });
        },

        // 初始化销售趋势图表
        initSalesChart() {
            const salesCanvas = document.getElementById('salesChart');
            if (!salesCanvas) {
                console.log('销售图表元素未找到，跳过初始化');
                return;
            }

            const ctx = salesCanvas.getContext('2d');
            this.salesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '销售额',
                        data: [0, 0, 0, 0, 0, 0],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '近6个月销售趋势'
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '¥' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        },

        // 初始化分类分布图表
        initCategoryChart() {
            const categoryCanvas = document.getElementById('categoryChart');
            if (!categoryCanvas) {
                console.log('分类图表元素未找到，跳过初始化');
                return;
            }

            const ctx = categoryCanvas.getContext('2d');
            this.categoryChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['护肤品', '彩妆', '香水', '美发', '身体护理'],
                    datasets: [{
                        data: [0, 0, 0, 0, 0],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '产品分类分布'
                        },
                        legend: {
                            display: true,
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }
});