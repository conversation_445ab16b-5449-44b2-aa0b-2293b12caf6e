<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户认证测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>用户认证系统测试</h1>

    <div class="test-section">
        <h3>1. 测试登录功能</h3>
        <input type="text" id="loginUser" placeholder="用户名/手机号" value="15120248009">
        <input type="password" id="loginPass" placeholder="密码" value="200117Wc$">
        <button onclick="testLogin()">测试登录</button>
        <div class="result" id="loginResult"></div>
    </div>

    <div class="test-section">
        <h3>2. 测试用户信息获取</h3>
        <input type="text" id="userIdentifier" placeholder="用户ID或手机号" value="15120248009">
        <button onclick="testGetUserInfo()">获取用户信息</button>
        <div class="result" id="userInfoResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 测试购物车功能</h3>
        <input type="text" id="cartUserPhone" placeholder="用户手机号" value="15120248009">
        <button onclick="testGetCart()">获取购物车</button>
        <button onclick="testAddToCart()">添加商品到购物车</button>
        <div class="result" id="cartResult"></div>
    </div>

    <div class="test-section">
        <h3>4. 本地存储测试</h3>
        <button onclick="testLocalStorage()">查看本地存储</button>
        <button onclick="clearLocalStorage()">清除本地存储</button>
        <div class="result" id="storageResult"></div>
    </div>

    <div class="test-section">
        <h3>5. 页面跳转测试</h3>
        <button onclick="goToIndex()">跳转到主页</button>
        <button onclick="goToLogin()">跳转到登录页</button>
    </div>

    <script>
        // 测试登录功能
        function testLogin() {
            const username = document.getElementById('loginUser').value;
            const password = document.getElementById('loginPass').value;
            
            axios.post('/user/login', {
                userId: username,
                phone: username,
                password: password
            })
            .then(response => {
                document.getElementById('loginResult').textContent = 
                    '登录结果：\n' + JSON.stringify(response.data, null, 2);
                
                // 如果登录成功，保存用户信息
                if (response.data.success && response.data.user) {
                    const userData = {
                        ...response.data.user,
                        loginTime: Date.now()
                    };
                    localStorage.setItem('sky_current_user', JSON.stringify(userData));
                }
            })
            .catch(error => {
                document.getElementById('loginResult').textContent = 
                    '登录错误：\n' + error.message;
            });
        }

        // 测试获取用户信息
        function testGetUserInfo() {
            const identifier = document.getElementById('userIdentifier').value;
            
            axios.get(`/api/user/info/${identifier}`)
            .then(response => {
                document.getElementById('userInfoResult').textContent = 
                    '用户信息：\n' + JSON.stringify(response.data, null, 2);
            })
            .catch(error => {
                document.getElementById('userInfoResult').textContent = 
                    '获取用户信息错误：\n' + error.message;
            });
        }

        // 测试获取购物车
        function testGetCart() {
            const userPhone = document.getElementById('cartUserPhone').value;
            
            axios.get(`/api/cart/${userPhone}`)
            .then(response => {
                document.getElementById('cartResult').textContent = 
                    '购物车信息：\n' + JSON.stringify(response.data, null, 2);
            })
            .catch(error => {
                document.getElementById('cartResult').textContent = 
                    '获取购物车错误：\n' + error.message;
            });
        }

        // 测试添加商品到购物车
        function testAddToCart() {
            const userPhone = document.getElementById('cartUserPhone').value;
            
            const cartData = {
                userPhone: userPhone,
                productId: 2,
                quantity: 1
            };
            
            axios.post('/api/cart/add', cartData)
            .then(response => {
                document.getElementById('cartResult').textContent = 
                    '添加购物车结果：\n' + JSON.stringify(response.data, null, 2);
            })
            .catch(error => {
                document.getElementById('cartResult').textContent = 
                    '添加购物车错误：\n' + error.message;
            });
        }

        // 测试本地存储
        function testLocalStorage() {
            const currentUser = localStorage.getItem('sky_current_user');
            const result = currentUser ? 
                '当前用户信息：\n' + JSON.stringify(JSON.parse(currentUser), null, 2) :
                '本地存储中没有用户信息';
            document.getElementById('storageResult').textContent = result;
        }

        // 清除本地存储
        function clearLocalStorage() {
            localStorage.removeItem('sky_current_user');
            document.getElementById('storageResult').textContent = '本地存储已清除';
        }

        // 页面跳转
        function goToIndex() {
            window.location.href = 'index.html';
        }

        function goToLogin() {
            window.location.href = 'land.html';
        }

        // 页面加载时检查本地存储
        window.onload = function() {
            testLocalStorage();
        };
    </script>
</body>
</html>
