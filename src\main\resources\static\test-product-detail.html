<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品详情测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            background: #06b6d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0891b2;
        }
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .product-price {
            color: #e74c3c;
            font-size: 1.2em;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>产品详情功能测试</h1>
        
        <div class="test-section">
            <h3>🧪 测试控制</h3>
            <button class="btn" onclick="loadProducts()">加载产品列表</button>
            <button class="btn" onclick="testProductDetail(1)">测试产品详情(ID:1)</button>
            <button class="btn" onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h3>📦 产品列表</h3>
            <div id="productList" class="product-list">
                <p>点击"加载产品列表"按钮获取产品</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="logArea" class="log-area"></div>
        </div>
    </div>

    <script src="index_new.js"></script>
    <script>
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        // 清空日志
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }

        // 加载产品列表
        function loadProducts() {
            log('🔄 开始加载产品列表...');
            
            axios.get('/api/products')
                .then(response => {
                    log('✅ 产品列表API响应: ' + JSON.stringify(response.data, null, 2));
                    
                    if (response.data.success && response.data.data) {
                        displayProducts(response.data.data);
                    } else {
                        log('❌ 产品列表数据为空或获取失败');
                    }
                })
                .catch(error => {
                    log('❌ 加载产品列表失败: ' + error.message);
                });
        }

        // 显示产品列表
        function displayProducts(products) {
            const productList = document.getElementById('productList');
            
            if (!products || products.length === 0) {
                productList.innerHTML = '<p>没有找到产品</p>';
                return;
            }

            const productsHTML = products.slice(0, 8).map(product => `
                <div class="product-card" onclick="testProductDetail(${product.id})">
                    <img src="${getImageUrl(product.image_url || product.imageUrl)}" 
                         alt="${product.name}" 
                         class="product-image"
                         onerror="this.src='${getImageUrl(null)}'">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">¥${product.price}</div>
                </div>
            `).join('');

            productList.innerHTML = productsHTML;
            log(`📦 显示了 ${products.length} 个产品`);
        }

        // 测试产品详情
        function testProductDetail(productId) {
            log(`🔍 测试产品详情，产品ID: ${productId}`);
            
            // 首先获取产品信息
            axios.get(`/api/products/${productId}`)
                .then(response => {
                    log('📋 产品信息API响应: ' + JSON.stringify(response.data, null, 2));
                    
                    if (response.data.success && response.data.data) {
                        const product = response.data.data;
                        log('🎯 开始显示产品详情模态框...');
                        
                        // 调用产品详情显示函数
                        if (typeof showProductDetail === 'function') {
                            showProductDetail(product);
                        } else {
                            log('❌ showProductDetail 函数未找到');
                        }
                    } else {
                        log('❌ 产品信息获取失败');
                    }
                })
                .catch(error => {
                    log('❌ 获取产品信息失败: ' + error.message);
                });
        }

        // 模拟用户登录状态
        window.userCenter = {
            authManager: {
                isLoggedIn: () => true,
                getCurrentUser: () => ({
                    phone: '13800138000',
                    name: '测试用户'
                })
            },
            showMessage: (message, type) => {
                log(`💬 消息[${type}]: ${message}`);
                alert(`[${type}] ${message}`);
            }
        };

        // 页面加载完成后自动加载产品
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 测试页面加载完成');
            loadProducts();
        });
    </script>
</body>
</html>
