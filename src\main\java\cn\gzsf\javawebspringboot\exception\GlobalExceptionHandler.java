package cn.gzsf.javawebspringboot.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 * 统一处理应用程序中的异常，提供友好的错误响应
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleException(Exception e, HttpServletRequest request) {
        logger.error("系统异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "系统内部错误，请稍后重试");
        result.put("error", e.getMessage());
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        logger.error("空指针异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "数据异常，请检查输入参数");
        result.put("error", "NullPointerException");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        logger.error("参数异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "参数错误: " + e.getMessage());
        result.put("error", "IllegalArgumentException");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
        logger.error("文件上传大小超限: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "上传文件大小超过限制，请选择小于10MB的文件");
        result.put("error", "MaxUploadSizeExceededException");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        logger.error("运行时异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "操作失败: " + e.getMessage());
        result.put("error", "RuntimeException");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler(org.springframework.dao.DataAccessException.class)
    @ResponseBody
    public ResponseEntity<Map<String, Object>> handleDataAccessException(org.springframework.dao.DataAccessException e, HttpServletRequest request) {
        logger.error("数据库异常: {}, 请求URL: {}", e.getMessage(), request.getRequestURL(), e);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "数据库操作失败，请稍后重试");
        result.put("error", "DataAccessException");
        result.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
