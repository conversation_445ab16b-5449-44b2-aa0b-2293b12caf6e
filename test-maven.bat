@echo off
echo ================================
echo Testing Maven with JDK 1.8
echo ================================

REM 设置JDK 1.8路径
set "JAVA_HOME=D:\Program Files (x86)\JAVA\jdk"
set "MAVEN_HOME=%cd%\apache-maven-3.9.4"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%
echo.

echo Testing Maven help...
"%MAVEN_HOME%\bin\mvn.cmd" help:help

echo.
echo Maven test completed!
pause
