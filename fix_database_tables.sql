-- 修复数据库表结构问题
-- 确保所有必要的表都存在且结构正确

-- 1. 确保products表存在（如果是product表则重命名）
-- 检查是否存在product表，如果存在则重命名为products
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'product');

SET @sql = IF(@table_exists > 0, 
              'RENAME TABLE product TO products', 
              'SELECT "product table does not exist" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 创建products表（如果不存在）
CREATE TABLE IF NOT EXISTS `products` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品ID',
  `name` varchar(200) NOT NULL COMMENT '产品名称',
  `description` text DEFAULT NULL COMMENT '产品描述',
  `price` decimal(10,2) NOT NULL COMMENT '产品价格',
  `stock` int(11) DEFAULT 0 COMMENT '库存数量',
  `image_url` varchar(500) DEFAULT NULL COMMENT '主图片URL',
  `is_new` tinyint(1) DEFAULT 0 COMMENT '是否新品',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_price` (`price`),
  KEY `idx_is_new` (`is_new`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品表';

-- 3. 确保user表存在
CREATE TABLE IF NOT EXISTS `user` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
  `user_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '用户账号',
  `username` VARCHAR(100) NOT NULL COMMENT '用户名',
  `phone` VARCHAR(20) UNIQUE NOT NULL COMMENT '手机号',
  `password` VARCHAR(255) NOT NULL COMMENT '密码',
  `avatar` VARCHAR(255) DEFAULT '/images/avatar/default-avatar.jpg' COMMENT '用户头像',
  `signature` VARCHAR(500) DEFAULT '' COMMENT '用户签名',
  `register_time` BIGINT NOT NULL COMMENT '注册时间',
  `created_time` BIGINT DEFAULT 0 COMMENT '创建时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  INDEX `idx_phone` (`phone`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 4. 确保categories表存在
CREATE TABLE IF NOT EXISTS `categories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用(0:否, 1:是)',
  `created_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分类表';

-- 5. 确保product_favorite表存在
CREATE TABLE IF NOT EXISTS `product_favorite` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `user_phone` VARCHAR(20) NOT NULL COMMENT '用户手机号',
    `product_id` BIGINT NOT NULL COMMENT '产品ID',
    `created_time` BIGINT NOT NULL COMMENT '创建时间',
    `updated_time` BIGINT NOT NULL COMMENT '更新时间',
    INDEX `idx_user_phone` (`user_phone`),
    INDEX `idx_product_id` (`product_id`),
    UNIQUE KEY `uk_user_product` (`user_phone`, `product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品收藏表';

-- 6. 确保product_comment表存在
CREATE TABLE IF NOT EXISTS `product_comment` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `product_id` BIGINT NOT NULL COMMENT '产品ID',
    `user_phone` VARCHAR(20) NOT NULL COMMENT '用户手机号',
    `user_name` VARCHAR(50) COMMENT '用户名称',
    `user_avatar` VARCHAR(255) COMMENT '用户头像',
    `content` TEXT NOT NULL COMMENT '评论内容',
    `rating` INT DEFAULT 5 COMMENT '评分(1-5星)',
    `images` TEXT COMMENT '评论图片，多个图片用逗号分隔',
    `created_time` BIGINT NOT NULL COMMENT '创建时间',
    `updated_time` BIGINT NOT NULL COMMENT '更新时间',
    `status` INT DEFAULT 0 COMMENT '状态：0-待审核，1-已通过，2-已拒绝',
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_user_phone` (`user_phone`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品评论表';

-- 7. 确保product_share表存在
CREATE TABLE IF NOT EXISTS `product_share` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `product_id` BIGINT NOT NULL COMMENT '产品ID',
    `user_phone` VARCHAR(20) NOT NULL COMMENT '用户手机号',
    `share_type` VARCHAR(20) NOT NULL COMMENT '分享类型：wechat, weibo, qq, link等',
    `share_url` VARCHAR(500) COMMENT '分享链接',
    `created_time` BIGINT NOT NULL COMMENT '创建时间',
    INDEX `idx_product_id` (`product_id`),
    INDEX `idx_user_phone` (`user_phone`),
    INDEX `idx_share_type` (`share_type`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分享表';

-- 8. 插入一些测试产品数据（如果products表为空）
INSERT IGNORE INTO `products` (`id`, `name`, `description`, `price`, `stock`, `image_url`, `is_new`) VALUES
(1, '美白精华液', '深层美白，淡化色斑，让肌肤焕发自然光彩', 299.00, 100, '/images/product1.jpg', 1),
(2, '保湿面霜', '24小时长效保湿，滋润肌肤，改善干燥', 199.00, 150, '/images/product2.jpg', 0),
(3, '防晒霜SPF50+', '高倍防晒，有效阻挡紫外线伤害', 159.00, 200, '/images/product3.jpg', 1),
(4, '洁面乳', '温和清洁，深层去污，不紧绷', 89.00, 300, '/images/product4.jpg', 0),
(5, '眼霜', '淡化细纹，紧致眼部肌肤', 399.00, 80, '/images/product5.jpg', 1);

-- 9. 插入一些测试分类数据（如果categories表为空）
INSERT IGNORE INTO `categories` (`id`, `name`, `description`, `sort_order`, `is_active`, `created_time`, `updated_time`) VALUES
(1, '护肤', '各类护肤产品', 1, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(2, '彩妆', '彩妆化妆品', 2, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(3, '香水', '各种香水产品', 3, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
(4, '轮播图', '首页轮播图片', 99, 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 10. 插入一些测试用户数据（如果user表为空）
INSERT IGNORE INTO `user` (`user_id`, `username`, `phone`, `password`, `register_time`, `created_time`) VALUES
('admin', '管理员', '15120248009', 'admin123', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user001', '小雯', '18823912577', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user002', 'KKKK', '18796247689', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user003', '测试用户1', '13800138001', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('user004', '测试用户2', '13800138002', '123456', UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 11. 插入一些测试收藏数据
INSERT IGNORE INTO `product_favorite` (`user_phone`, `product_id`, `created_time`, `updated_time`) VALUES
('13800138001', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138001', 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138002', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

-- 12. 插入一些测试评论数据
INSERT IGNORE INTO `product_comment` (`product_id`, `user_phone`, `user_name`, `user_avatar`, `content`, `rating`, `created_time`, `updated_time`, `status`) VALUES
(1, '13800138001', '测试用户1', '/images/avatars/default-avatar.jpg', '这个产品非常好用，推荐大家购买！', 5, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1),
(1, '13800138002', '测试用户2', '/images/avatars/default-avatar.jpg', '质量不错，物流很快。', 4, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1),
(2, '13800138001', '测试用户1', '/images/avatars/default-avatar.jpg', '包装精美，产品质量很好。', 5, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1);

-- 13. 插入一些测试分享数据
INSERT IGNORE INTO `product_share` (`product_id`, `user_phone`, `share_type`, `share_url`, `created_time`) VALUES
(1, '13800138001', 'wechat', 'https://example.com/product/1', UNIX_TIMESTAMP() * 1000),
(1, '13800138002', 'weibo', 'https://example.com/product/1', UNIX_TIMESTAMP() * 1000),
(2, '13800138001', 'qq', 'https://example.com/product/2', UNIX_TIMESTAMP() * 1000);

-- 完成
SELECT 'Database tables fixed successfully!' as message;
