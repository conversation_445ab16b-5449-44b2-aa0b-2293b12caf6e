package cn.gzsf.javawebspringboot.service;

import cn.gzsf.javawebspringboot.entity.Product;

import java.util.List;

// 产品服务接口，定义产品相关的业务方法
public interface ProductService {
    // 获取所有产品
    List<Product> getAllProducts();
    // 根据关键词搜索产品
    List<Product> searchProducts(String keyword);
    // 更新产品信息
    boolean updateProduct(Product product);
    // 新增产品
    boolean addProduct(Product product);


    // 删除产品信息
    boolean deleteProduct(Long id);

    // 根据 ID 获取产品信息
    Product getProductById(Long id);

    // 分页获取产品列表
    List<Product> getProductsPage(int offset, int size);

    // 获取产品总数
    int getTotalProductCount();

    // 获取新品产品
    List<Product> getNewProducts(int limit);

    // 根据分类获取产品
    List<Product> getProductsByCategory(Long categoryId);
}

