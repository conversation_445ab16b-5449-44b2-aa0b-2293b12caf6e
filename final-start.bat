@echo off
echo ================================
echo Final SpringBoot Startup
echo ================================

REM 设置JDK 1.8路径
set "JAVA_HOME=D:\Program Files (x86)\JAVA\jdk"
set "MAVEN_HOME=%cd%\apache-maven-3.9.4"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%
echo Current Directory: %cd%
echo.

echo Testing Java...
java -version
echo.

echo Testing Maven...
mvn --version
echo.

echo Compiling and starting SpringBoot application...
echo This may take a few minutes for the first time...
echo Port: 8082
echo URL: http://localhost:8082
echo ================================

mvn clean compile spring-boot:run -X

echo.
echo Application ended.
pause
