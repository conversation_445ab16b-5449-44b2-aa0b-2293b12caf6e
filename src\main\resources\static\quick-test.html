<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>快速分类测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; font-family: monospace; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 快速分类测试</h1>
    
    <button class="btn" onclick="testCategoryAPI()">测试分类API</button>
    <button class="btn" onclick="testProductAPI()">测试产品API</button>
    
    <div id="result" class="result">点击按钮开始测试...</div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        function showResult(content) {
            document.getElementById('result').textContent = content;
            console.log(content);
        }

        async function testCategoryAPI() {
            try {
                showResult('正在测试分类API...');
                
                const response = await axios.get('/admin/category/all');
                
                const result = `分类API测试结果:
状态: ${response.status}
数据类型: ${typeof response.data}
是否为数组: ${Array.isArray(response.data)}
数据长度: ${response.data?.length || '未知'}

原始数据:
${JSON.stringify(response.data, null, 2)}`;
                
                showResult(result);
                
            } catch (error) {
                showResult(`分类API测试失败: ${error.message}\n${error.response?.data || ''}`);
            }
        }

        async function testProductAPI() {
            try {
                showResult('正在测试产品API...');
                
                // 测试全部产品
                const allResponse = await axios.get('/products/category/0');
                
                // 测试分类1的产品
                const cat1Response = await axios.get('/products/category/1');
                
                const result = `产品API测试结果:

全部产品 (ID: 0):
成功: ${allResponse.data.success}
数量: ${allResponse.data.data?.length || 0}

分类1产品 (ID: 1):
成功: ${cat1Response.data.success}
数量: ${cat1Response.data.data?.length || 0}

全部产品数据:
${JSON.stringify(allResponse.data, null, 2)}

分类1产品数据:
${JSON.stringify(cat1Response.data, null, 2)}`;
                
                showResult(result);
                
            } catch (error) {
                showResult(`产品API测试失败: ${error.message}\n${error.response?.data || ''}`);
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            testCategoryAPI();
        };
    </script>
</body>
</html>
