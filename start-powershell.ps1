# SpringBoot启动脚本 - PowerShell版本
Write-Host "================================" -ForegroundColor Cyan
Write-Host "SpringBoot Application Startup" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 设置环境变量
$env:JAVA_HOME = "D:\Program Files (x86)\JAVA\jdk"
$env:MAVEN_HOME = "$PWD\apache-maven-3.9.4"
$env:PATH = "$env:JAVA_HOME\bin;$env:MAVEN_HOME\bin;$env:PATH"

Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor White
Write-Host "MAVEN_HOME: $env:MAVEN_HOME" -ForegroundColor White
Write-Host "Current Directory: $PWD" -ForegroundColor White
Write-Host ""

Write-Host "Testing Java..." -ForegroundColor Yellow
& java -version
Write-Host ""

Write-Host "Testing Maven..." -ForegroundColor Yellow
& mvn --version
Write-Host ""

Write-Host "Starting SpringBoot application..." -ForegroundColor Green
Write-Host "Port: 8082" -ForegroundColor Cyan
Write-Host "URL: http://localhost:8082" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 启动应用
& mvn spring-boot:run

Write-Host ""
Write-Host "Application ended." -ForegroundColor Red
