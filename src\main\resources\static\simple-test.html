<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔍 简单应用程序测试</h1>
    
    <p>如果您能看到这个页面，说明应用程序正在运行。</p>
    
    <button class="btn" onclick="testBasic()">测试基本功能</button>
    <button class="btn" onclick="testAPI()">测试API</button>
    
    <div id="result" class="result">点击按钮开始测试...</div>

    <script>
        function showResult(content) {
            document.getElementById('result').innerHTML = content;
            console.log(content);
        }

        function testBasic() {
            showResult(`
                <h3>✅ 基本功能测试成功</h3>
                <p>当前时间: ${new Date().toLocaleString()}</p>
                <p>页面URL: ${window.location.href}</p>
                <p>用户代理: ${navigator.userAgent}</p>
            `);
        }

        async function testAPI() {
            try {
                showResult('正在测试API...');
                
                // 测试分类API
                const response = await fetch('/admin/category/all');
                const data = await response.json();
                
                showResult(`
                    <h3>📊 API测试结果</h3>
                    <p><strong>状态:</strong> ${response.status}</p>
                    <p><strong>数据类型:</strong> ${typeof data}</p>
                    <p><strong>是否为数组:</strong> ${Array.isArray(data)}</p>
                    <p><strong>数据长度:</strong> ${data?.length || '未知'}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `);
                
            } catch (error) {
                showResult(`
                    <h3>❌ API测试失败</h3>
                    <p><strong>错误:</strong> ${error.message}</p>
                `);
            }
        }

        // 页面加载时自动测试
        window.onload = function() {
            testBasic();
        };
    </script>
</body>
</html>
