# PowerShell script to run Spring Boot application
Write-Host "Starting Spring Boot Application..." -ForegroundColor Green

# 设置classpath
$classpath = "target\classes"

# 添加Maven依赖（如果有的话）
if (Test-Path "apache-maven-3.9.4\lib") {
    $mavenLibs = Get-ChildItem "apache-maven-3.9.4\lib\*.jar" | ForEach-Object { $_.FullName }
    $classpath += ";" + ($mavenLibs -join ";")
}

Write-Host "Classpath: $classpath" -ForegroundColor Yellow

# 运行Spring Boot应用
Write-Host "Running Spring Boot Application..." -ForegroundColor Green
java -cp $classpath cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

Write-Host "Application stopped." -ForegroundColor Red
Read-Host "Press Enter to exit"
