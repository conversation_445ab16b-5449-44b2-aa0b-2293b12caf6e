@echo off
echo ========================================
echo    SpringBoot应用启动 (完整环境设置)
echo ========================================

REM 设置编码
chcp 65001 > nul

REM 进入项目目录
cd /d "%~dp0"

echo 🔧 项目目录: %CD%
echo.

REM 设置JAVA_HOME（请根据您的Java安装路径调整）
REM 常见的Java 8安装路径
if exist "C:\Program Files\Java\jdk1.8.0_202" (
    set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_202
) else if exist "C:\Program Files\Java\jdk1.8.0_301" (
    set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_301
) else if exist "C:\Program Files\Java\jdk1.8.0_311" (
    set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_311
) else if exist "C:\Program Files\Java\jdk-8" (
    set JAVA_HOME=C:\Program Files\Java\jdk-8
) else if exist "C:\Program Files (x86)\Java\jdk1.8.0_202" (
    set JAVA_HOME=C:\Program Files (x86)\Java\jdk1.8.0_202
) else (
    echo ⚠️ 未找到Java 8，尝试使用系统默认Java
    where java >nul 2>&1
    if errorlevel 1 (
        echo ❌ 系统中未找到Java，请安装Java 8
        pause
        exit /b 1
    )
)

REM 设置PATH
if defined JAVA_HOME (
    set PATH=%JAVA_HOME%\bin;%PATH%
    echo ✅ JAVA_HOME: %JAVA_HOME%
)

REM 检查Java版本
echo 🔍 Java版本检查:
java -version
echo.

REM 检查依赖目录
if not exist "target\dependency" (
    echo 📦 依赖目录不存在，开始复制依赖...
    if defined JAVA_HOME (
        .\apache-maven-3.9.4\bin\mvn.cmd dependency:copy-dependencies -DoutputDirectory=target/dependency
    ) else (
        echo ⚠️ 跳过Maven依赖复制，直接尝试启动
    )
)

REM 构建类路径
echo 📚 构建类路径...
set CLASSPATH=target\classes

REM 添加依赖jar包
if exist "target\dependency" (
    for %%i in (target\dependency\*.jar) do (
        call set CLASSPATH=%%CLASSPATH%%;%%i
    )
    echo ✅ 已添加依赖jar包
) else (
    echo ⚠️ 依赖目录不存在，可能会缺少依赖
)

echo.
echo 🚀 启动SpringBoot应用程序...
echo    主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo    端口: 8082
echo    访问地址: http://localhost:8082
echo.

REM JVM参数
set JVM_OPTS=-Dfile.encoding=UTF-8 -Dserver.port=8082 -Dspring.profiles.active=dev

echo 🌟 正在启动，请稍候...
echo.

REM 启动应用程序
java %JVM_OPTS% -cp "%CLASSPATH%" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

echo.
echo 📴 应用程序已停止
pause
