-- 创建用户地址表
CREATE TABLE IF NOT EXISTS `user_address` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_phone` varchar(20) NOT NULL COMMENT '用户手机号',
  `receiver_name` varchar(100) NOT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) NOT NULL COMMENT '收货人手机号',
  `province` varchar(50) NOT NULL COMMENT '省份',
  `city` varchar(50) NOT NULL COMMENT '城市',
  `district` varchar(50) NOT NULL COMMENT '区县',
  `detail_address` varchar(255) NOT NULL COMMENT '详细地址',
  `postal_code` varchar(10) DEFAULT NULL COMMENT '邮政编码',
  `is_default` tinyint(1) DEFAULT 0 COMMENT '是否默认地址：1-是，0-否',
  `created_time` bigint(20) NOT NULL COMMENT '创建时间',
  `updated_time` bigint(20) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_phone` (`user_phone`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户地址表';

-- 插入一些测试数据
INSERT INTO `user_address` (`user_phone`, `receiver_name`, `receiver_phone`, `province`, `city`, `district`, `detail_address`, `is_default`, `created_time`, `updated_time`) VALUES
('13800138000', '张三', '13800138000', '广东省', '广州市', '天河区', '天河路123号', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138000', '张三', '13800138000', '广东省', '深圳市', '南山区', '科技园456号', 0, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138001', '李四', '13800138001', '北京市', '北京市', '朝阳区', '朝阳路789号', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
