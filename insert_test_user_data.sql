-- 插入测试用户详情数据

-- 为测试用户添加详情信息
INSERT INTO user_detail (phone, avatar_url, signature, created_time, updated_time) 
VALUES ('15120248009', '/images/avatar/user1.jpg', '热爱美妆，追求精致生活 ✨', 1702234567890, 1702234567890)
ON DUPLICATE KEY UPDATE 
    avatar_url = VALUES(avatar_url),
    signature = VALUES(signature),
    updated_time = VALUES(updated_time);

-- 添加测试收货地址
INSERT INTO user_address (user_phone, receiver_name, receiver_phone, province, city, district, detail_address, postal_code, is_default, created_time, updated_time) 
VALUES 
('15120248009', '张小美', '15120248009', '北京市', '朝阳区', '望京街道', '望京SOHO T3座 1008室', '100102', TRUE, 1702234567890, 1702234567890),
('15120248009', '张小美', '15120248009', '上海市', '浦东新区', '陆家嘴街道', '世纪大道88号 金茂大厦 2008室', '200120', FALSE, 1702234567890, 1702234567890),
('15120248009', '李美丽', '13800138000', '广东省', '深圳市', '南山区', '科技园南区 腾讯大厦 35楼', '518057', FALSE, 1702234567890, 1702234567890);

-- 添加一些测试订单（如果user_order表存在）
INSERT INTO user_order (order_no, user_phone, total_amount, status, receiver_name, receiver_phone, receiver_address, remark, created_time, updated_time) 
VALUES 
('ORD20241210001', '15120248009', 299.00, 1, '张小美', '15120248009', '北京市朝阳区望京街道望京SOHO T3座 1008室', '请小心轻放', 1702234567890, 1702234567890),
('ORD20241210002', '15120248009', 599.00, 2, '张小美', '15120248009', '北京市朝阳区望京街道望京SOHO T3座 1008室', '', 1702134567890, 1702134567890),
('ORD20241210003', '15120248009', 128.00, 3, '张小美', '15120248009', '上海市浦东新区陆家嘴街道世纪大道88号', '工作日配送', 1702034567890, 1702034567890);

-- 添加一些购物车测试数据
INSERT INTO shopping_cart (user_phone, product_id, quantity, created_time, updated_time) 
VALUES 
('15120248009', 2, 2, 1702234567890, 1702234567890),
('15120248009', 22, 1, 1702234567890, 1702234567890)
ON DUPLICATE KEY UPDATE 
    quantity = VALUES(quantity),
    updated_time = VALUES(updated_time);

-- 创建好友关系表（如果不存在）
CREATE TABLE IF NOT EXISTS user_friends (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL,
    friend_phone VARCHAR(20) NOT NULL,
    friend_name VARCHAR(100),
    status TINYINT DEFAULT 1 COMMENT '1-好友 0-已删除',
    created_time BIGINT NOT NULL,
    updated_time BIGINT NOT NULL,
    UNIQUE KEY unique_friendship (user_phone, friend_phone),
    FOREIGN KEY (user_phone) REFERENCES user(phone) ON DELETE CASCADE
);

-- 添加一些测试好友
INSERT INTO user_friends (user_phone, friend_phone, friend_name, status, created_time, updated_time) 
VALUES 
('15120248009', '13800138001', '小红', 1, 1702234567890, 1702234567890),
('15120248009', '13800138002', '小明', 1, 1702234567890, 1702234567890),
('15120248009', '13800138003', '小丽', 1, 1702234567890, 1702234567890),
('15120248009', '13800138004', '小芳', 1, 1702234567890, 1702234567890),
('15120248009', '13800138005', '小华', 1, 1702234567890, 1702234567890);

-- 更新用户表，添加头像信息
UPDATE user SET avatar = '/images/avatar/user1.jpg', signature = '热爱美妆，追求精致生活 ✨' 
WHERE phone = '15120248009';
