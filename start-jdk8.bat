@echo off
echo ================================
echo SpringBoot Startup with JDK 1.8
echo ================================

REM 设置JDK 1.8路径
set JAVA_HOME=D:\Program Files (x86)\JAVA\jdk
set PATH=%JAVA_HOME%\bin;%PATH%

echo JAVA_HOME: %JAVA_HOME%
echo.

echo Checking Java version...
java -version
echo.

echo Setting Maven environment...
set MAVEN_HOME=%cd%\apache-maven-3.9.4
set PATH=%MAVEN_HOME%\bin;%PATH%

echo MAVEN_HOME: %MAVEN_HOME%
echo.

echo Compiling project with Maven...
mvn clean compile
echo.

echo Starting SpringBoot application...
echo Port: 8082
echo URL: http://localhost:8082
echo ================================

mvn spring-boot:run

pause
