-- 创建用户好友表
CREATE TABLE IF NOT EXISTS `user_friends` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_phone` varchar(20) NOT NULL COMMENT '用户手机号',
  `friend_phone` varchar(20) NOT NULL COMMENT '好友手机号',
  `friend_name` varchar(100) DEFAULT NULL COMMENT '好友昵称',
  `status` int(11) DEFAULT 1 COMMENT '状态：1-正常，0-已删除',
  `created_time` bigint(20) NOT NULL COMMENT '创建时间',
  `updated_time` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_phone` (`user_phone`),
  KEY `idx_friend_phone` (`friend_phone`),
  UNIQUE KEY `uk_user_friend` (`user_phone`, `friend_phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户好友表';

-- 插入一些测试数据
INSERT INTO `user_friends` (`user_phone`, `friend_phone`, `friend_name`, `status`, `created_time`, `updated_time`) VALUES
('13800138000', '13800138001', '张三', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138000', '13800138002', '李四', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138001', '13800138000', '王五', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);
