<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图上传测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #409EFF;
        }
        .upload-area.dragover {
            border-color: #409EFF;
            background-color: #f0f9ff;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .preview-image {
            max-width: 300px;
            max-height: 200px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #66b1ff;
        }
        button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎠 轮播图上传测试</h1>
        <p>测试轮播图专用上传接口：<code>/carousel/upload</code></p>

        <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
            <div>
                <i style="font-size: 48px; color: #c0c4cc;">📁</i>
                <p>点击选择文件或拖拽文件到此处</p>
                <p style="color: #999; font-size: 14px;">支持 .jpg、.jpeg、.png 格式，最大 2MB</p>
            </div>
        </div>

        <input type="file" id="fileInput" accept=".jpg,.jpeg,.png" style="display: none;" onchange="handleFileSelect(event)">

        <div>
            <button onclick="testUpload()" id="uploadBtn" disabled>上传轮播图</button>
            <button onclick="clearResult()">清空结果</button>
            <button onclick="testCarouselList()">获取轮播图列表</button>
        </div>

        <div id="preview"></div>
        <div id="result" class="result">选择文件后点击上传...</div>
    </div>

    <script>
        let selectedFile = null;

        // 文件选择处理
        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                showPreview(file);
                document.getElementById('uploadBtn').disabled = false;
            }
        }

        // 显示预览
        function showPreview(file) {
            const preview = document.getElementById('preview');
            const reader = new FileReader();
            
            reader.onload = function(e) {
                preview.innerHTML = `
                    <h3>预览:</h3>
                    <img src="${e.target.result}" class="preview-image" alt="预览图片">
                    <p>文件名: ${file.name}</p>
                    <p>文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p>文件类型: ${file.type}</p>
                `;
            };
            
            reader.readAsDataURL(file);
        }

        // 上传测试
        function testUpload() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', selectedFile);

            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在上传...';

            axios.post('/carousel/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            })
            .then(response => {
                console.log('上传响应:', response.data);
                let result = '上传成功！\n\n';
                result += JSON.stringify(response.data, null, 2);
                
                if (response.data.success && response.data.imageUrl) {
                    result += '\n\n访问URL: http://localhost:8082' + response.data.imageUrl;
                    result += '\n\n预览图片:';
                    
                    // 添加上传成功的图片预览
                    setTimeout(() => {
                        const img = document.createElement('img');
                        img.src = response.data.imageUrl;
                        img.className = 'preview-image';
                        img.alt = '上传成功的图片';
                        img.onload = () => {
                            resultDiv.textContent = result;
                            resultDiv.appendChild(document.createElement('br'));
                            resultDiv.appendChild(img);
                        };
                        img.onerror = () => {
                            resultDiv.textContent = result + '\n\n❌ 图片加载失败，请检查路径配置';
                        };
                    }, 500);
                }
                
                resultDiv.textContent = result;
            })
            .catch(error => {
                console.error('上传失败:', error);
                let result = '上传失败！\n\n';
                result += '错误信息: ' + error.message + '\n\n';
                if (error.response) {
                    result += '响应数据:\n' + JSON.stringify(error.response.data, null, 2);
                }
                resultDiv.textContent = result;
            });
        }

        // 获取轮播图列表
        function testCarouselList() {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = '正在获取轮播图列表...';

            axios.get('/carousel/images')
                .then(response => {
                    console.log('轮播图列表:', response.data);
                    resultDiv.textContent = '轮播图列表:\n\n' + JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('获取列表失败:', error);
                    resultDiv.textContent = '获取列表失败: ' + error.message;
                });
        }

        // 清空结果
        function clearResult() {
            document.getElementById('result').textContent = '结果已清空...';
            document.getElementById('preview').innerHTML = '';
            selectedFile = null;
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('fileInput').value = '';
        }

        // 拖拽上传支持
        const uploadArea = document.getElementById('uploadArea');

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    selectedFile = file;
                    showPreview(file);
                    document.getElementById('uploadBtn').disabled = false;
                } else {
                    alert('请选择图片文件');
                }
            }
        });
    </script>
</body>
</html>
