@echo off
echo ================================
echo Simple Maven Build with JDK 1.8
echo ================================

REM 设置JDK 1.8路径
set "JAVA_HOME=D:\Program Files (x86)\JAVA\jdk"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo JAVA_HOME: %JAVA_HOME%
echo.

echo Testing Java...
java -version
echo.

echo Testing Maven...
".\apache-maven-3.9.4\bin\mvn.cmd" --version
echo.

echo Compiling project...
".\apache-maven-3.9.4\bin\mvn.cmd" clean compile

echo.
echo Build completed!
pause
