<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试工具</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API测试工具</h1>
        
        <div class="test-item">
            <h3>1. 测试产品图片接口</h3>
            <div>
                <label>产品ID: </label>
                <input type="number" id="productId" value="1" min="1">
                <button onclick="testProductImages()">获取产品图片</button>
                <button onclick="testAllProducts()">测试所有产品</button>
            </div>
            <div id="images-result" class="result">点击按钮开始测试...</div>
        </div>

        <div class="test-item">
            <h3>2. 测试产品列表接口</h3>
            <div>
                <button onclick="testProductList()">获取产品列表</button>
            </div>
            <div id="products-result" class="result">点击按钮开始测试...</div>
        </div>

        <div class="test-item">
            <h3>3. 创建测试数据</h3>
            <div>
                <button onclick="createTestData()">创建测试图片数据</button>
                <button onclick="createCarouselData()">创建轮播图数据</button>
                <button onclick="checkDatabase()">检查数据库状态</button>
            </div>
            <div id="test-data-result" class="result">点击按钮开始测试...</div>
        </div>

        <div class="test-item">
            <h3>4. 测试轮播图接口</h3>
            <div>
                <button onclick="testCarouselImages()">获取轮播图列表</button>
                <button onclick="testActiveCarouselImages()">获取启用轮播图</button>
            </div>
            <div id="carousel-result" class="result">点击按钮开始测试...</div>
        </div>
    </div>

    <script>
        function testProductImages() {
            const productId = document.getElementById('productId').value;
            const resultDiv = document.getElementById('images-result');
            
            if (!productId) {
                resultDiv.textContent = '请输入产品ID';
                return;
            }

            resultDiv.textContent = '正在获取产品图片...';

            axios.get(`/products/${productId}/images`)
                .then(response => {
                    console.log('产品图片响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('获取产品图片失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function testAllProducts() {
            const resultDiv = document.getElementById('images-result');
            resultDiv.textContent = '正在测试所有产品的图片...';

            // 测试产品ID 1-10
            const promises = [];
            for (let i = 1; i <= 10; i++) {
                promises.push(
                    axios.get(`/products/${i}/images`)
                        .then(response => ({ productId: i, success: true, data: response.data }))
                        .catch(error => ({ productId: i, success: false, error: error.message }))
                );
            }

            Promise.all(promises).then(results => {
                let output = '所有产品图片测试结果:\n\n';
                results.forEach(result => {
                    output += `产品 ${result.productId}: `;
                    if (result.success) {
                        const imageCount = result.data.data ? result.data.data.length : 0;
                        output += `成功 (${imageCount} 张图片)\n`;
                        if (imageCount > 0) {
                            result.data.data.forEach((img, index) => {
                                output += `  图片${index + 1}: ${img.imageUrl} (主图: ${img.isPrimary})\n`;
                            });
                        }
                    } else {
                        output += `失败 - ${result.error}\n`;
                    }
                    output += '\n';
                });
                resultDiv.textContent = output;
            });
        }

        function testProductList() {
            const resultDiv = document.getElementById('products-result');
            resultDiv.textContent = '正在获取产品列表...';

            axios.get('/products/page?page=1&size=5')
                .then(response => {
                    console.log('产品列表响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('获取产品列表失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function createTestData() {
            const resultDiv = document.getElementById('test-data-result');
            resultDiv.textContent = '正在创建测试数据...';

            axios.post('/fix/create-product-images-table')
                .then(response => {
                    console.log('创建测试数据响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('创建测试数据失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function createCarouselData() {
            const resultDiv = document.getElementById('test-data-result');
            resultDiv.textContent = '正在创建轮播图数据...';

            axios.post('/fix/create-carousel-data')
                .then(response => {
                    console.log('创建轮播图数据响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('创建轮播图数据失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function testCarouselImages() {
            const resultDiv = document.getElementById('carousel-result');
            resultDiv.textContent = '正在获取轮播图列表...';

            axios.get('/carousel/images')
                .then(response => {
                    console.log('轮播图列表响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('获取轮播图列表失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function testActiveCarouselImages() {
            const resultDiv = document.getElementById('carousel-result');
            resultDiv.textContent = '正在获取启用轮播图...';

            axios.get('/carousel/active-images')
                .then(response => {
                    console.log('启用轮播图响应:', response.data);
                    resultDiv.textContent = JSON.stringify(response.data, null, 2);
                })
                .catch(error => {
                    console.error('获取启用轮播图失败:', error);
                    resultDiv.textContent = '错误: ' + error.message + '\n\n' + (error.response ? JSON.stringify(error.response.data, null, 2) : '');
                });
        }

        function checkDatabase() {
            const resultDiv = document.getElementById('test-data-result');
            resultDiv.textContent = '正在检查数据库状态...';

            // 检查产品表
            axios.get('/products/page?page=1&size=3')
                .then(response => {
                    let output = '数据库状态检查:\n\n';
                    output += '产品表状态:\n';
                    output += JSON.stringify(response.data, null, 2);
                    output += '\n\n正在检查图片表...';
                    resultDiv.textContent = output;

                    // 检查图片表
                    return axios.get('/products/1/images');
                })
                .then(response => {
                    let currentText = resultDiv.textContent;
                    currentText += '\n\n产品1的图片数据:\n';
                    currentText += JSON.stringify(response.data, null, 2);
                    resultDiv.textContent = currentText;
                })
                .catch(error => {
                    console.error('检查数据库失败:', error);
                    resultDiv.textContent += '\n\n错误: ' + error.message;
                });
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkDatabase();
        };
    </script>
</body>
</html>
