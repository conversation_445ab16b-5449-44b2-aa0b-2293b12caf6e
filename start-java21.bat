@echo off
echo ========================================
echo   SpringBoot启动 (Java 21兼容模式)
echo ========================================

REM 设置编码
chcp 65001 > nul

REM 进入项目目录
cd /d "%~dp0"

echo 🔧 项目目录: %CD%
echo.

REM 检查Java版本
echo 🔍 Java版本检查:
java -version
echo.

REM 检查主类文件
if not exist "target\classes\cn\gzsf\javawebspringboot\JavaWebSpringBootApplication.class" (
    echo ❌ 主类文件不存在，请先编译项目
    echo 提示: 可以在IDEA中点击 Build -> Build Project
    pause
    exit /b 1
)

echo ✅ 主类文件存在
echo.

REM 构建基本类路径
set CLASSPATH=target\classes

REM 尝试添加依赖（如果存在）
if exist "target\dependency" (
    echo 📚 添加依赖jar包...
    for %%i in (target\dependency\*.jar) do (
        call set CLASSPATH=%%CLASSPATH%%;%%i
    )
    echo ✅ 依赖已添加
) else (
    echo ⚠️ 依赖目录不存在，尝试基本启动
)

echo.
echo 🚀 启动SpringBoot应用程序...
echo    主类: cn.gzsf.javawebspringboot.JavaWebSpringBootApplication
echo    Java版本: 21 (兼容模式)
echo    端口: 8082
echo.

REM Java 21兼容参数
set JVM_OPTS=-Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Dserver.port=8082
set JVM_OPTS=%JVM_OPTS% -Dspring.profiles.active=dev
set JVM_OPTS=%JVM_OPTS% --add-opens java.base/java.lang=ALL-UNNAMED
set JVM_OPTS=%JVM_OPTS% --add-opens java.base/java.util=ALL-UNNAMED

echo 🌟 正在启动，请稍候...
echo    访问地址: http://localhost:8082
echo    按 Ctrl+C 停止应用程序
echo.

REM 启动应用程序
java %JVM_OPTS% -cp "%CLASSPATH%" cn.gzsf.javawebspringboot.JavaWebSpringBootApplication

echo.
echo 📴 应用程序已停止
pause
