<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.gzsf.javawebspringboot.dao.ProductDao">

    <!-- 定义结果映射，解决数据库字段名与Java属性名不一致的问题 -->
    <resultMap id="ProductResultMap" type="cn.gzsf.javawebspringboot.entity.Product">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DOUBLE"/>
        <result column="stock" property="stock" jdbcType="INTEGER"/>
        <result column="image_url" property="imageUrl" jdbcType="VARCHAR"/>
        <result column="is_new" property="isNew" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 查询所有商品 -->
    <select id="findAllProducts" resultMap="ProductResultMap">
        SELECT id, name, description, price, stock, image_url, is_new FROM products
    </select>
    <!-- 根据关键词模糊搜索商品 -->
    <select id="searchProducts" resultMap="ProductResultMap">
        SELECT id, name, description, price, stock, image_url, is_new FROM products
        WHERE name LIKE CONCAT('%', #{keyword}, '%')
           OR description LIKE CONCAT('%', #{keyword}, '%')
    </select>

    <!-- 更新编辑后产品信息 -->
    <update id="updateProduct" parameterType="cn.gzsf.javawebspringboot.entity.Product">
        UPDATE products
        SET name = #{name},
            description = #{description},
            price = #{price},
            stock = #{stock},
            image_url = #{imageUrl},
            is_new = #{isNew}
        WHERE id = #{id}
    </update>



    <!--插入新增产品信息 -->
    <insert id="addProduct" parameterType="cn.gzsf.javawebspringboot.entity.Product" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO products (name, description, price, stock, image_url, is_new)
        VALUES (#{name}, #{description}, #{price}, #{stock}, #{imageUrl}, #{isNew})
    </insert>

    <!-- 删除 product_category 表中与产品关联的记录 -->
    <delete id="deleteProductCategoriesByProductId" parameterType="java.lang.Long">
        DELETE FROM product_category WHERE product_id = #{product_id}
    </delete>

<!--删除产品信息-->
    <delete id="deleteProduct" parameterType="java.lang.Long">
        DELETE FROM products WHERE id = #{id}
    </delete>


    <!-- 根据 ID 查询产品 -->
    <select id="findProductById" resultMap="ProductResultMap">
        SELECT id, name, description, price, stock, image_url, is_new FROM products WHERE id = #{id}
    </select>

    <!-- 分页查询产品 -->
    <select id="findProductsPage" resultMap="ProductResultMap">
        SELECT id, name, description, price, stock, image_url, is_new FROM products
        ORDER BY id DESC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 获取产品总数 -->
    <select id="getTotalProductCount" resultType="int">
        SELECT COUNT(*) FROM products
    </select>

    <!-- 获取新品产品 -->
    <select id="getNewProducts" resultMap="ProductResultMap">
        SELECT id, name, description, price, stock, image_url, is_new
        FROM products
        WHERE is_new = 1
        ORDER BY id DESC
        LIMIT #{limit}
    </select>

    <!-- 根据分类获取产品 -->
    <select id="getProductsByCategory" resultMap="ProductResultMap">
        SELECT p.id, p.name, p.description, p.price, p.stock, p.image_url, p.is_new
        FROM products p
        INNER JOIN product_category pc ON p.id = pc.product_id
        WHERE pc.category_id = #{categoryId}
        ORDER BY p.id DESC
    </select>

</mapper>