<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品图片接口测试</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .product-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
            text-align: center;
        }
        .image-item img {
            max-width: 100%;
            height: 150px;
            object-fit: contain;
            border-radius: 3px;
        }
        .status {
            margin-top: 5px;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .primary-badge {
            background: #ffd700;
            color: #333;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 产品图片接口测试</h1>
        <p>测试产品图片获取接口的数据格式和图片显示</p>

        <div class="test-section">
            <h2>1. 获取产品列表</h2>
            <button onclick="loadProducts()">加载产品列表</button>
            <div id="products-list"></div>
        </div>

        <div class="test-section">
            <h2>2. 测试产品图片接口</h2>
            <div>
                <label>产品ID: </label>
                <input type="number" id="productId" value="1" min="1">
                <button onclick="testProductImages()">获取产品图片</button>
            </div>
            <div id="images-result"></div>
        </div>

        <div class="test-section">
            <h2>3. 接口响应数据</h2>
            <div id="json-response" class="json-display">点击上面的按钮查看接口响应数据...</div>
        </div>
    </div>

    <script>
        function showStatus(element, status) {
            const statusDiv = element.parentElement.querySelector('.status');
            if (statusDiv) {
                statusDiv.className = 'status ' + status;
                statusDiv.textContent = status === 'success' ? '✅ 加载成功' : '❌ 加载失败';
            }
        }

        function loadProducts() {
            const resultsDiv = document.getElementById('products-list');
            resultsDiv.innerHTML = '<p>加载中...</p>';

            axios.get('/products/page?page=1&size=10')
                .then(response => {
                    console.log('产品列表响应:', response.data);
                    document.getElementById('json-response').textContent = JSON.stringify(response.data, null, 2);

                    if (response.data.success && response.data.data) {
                        let html = '<h3>产品列表:</h3>';
                        response.data.data.forEach(product => {
                            html += `
                                <div class="product-item">
                                    <strong>ID: ${product.id}</strong> - ${product.name}
                                    <br>价格: ¥${product.price} | 库存: ${product.stock || '未设置'}
                                    <br>主图URL: ${product.imageUrl || product.primary_image_url || '无'}
                                    <button onclick="testProductImages(${product.id})" style="margin-top: 5px;">查看图片</button>
                                </div>
                            `;
                        });
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = '<p style="color: red;">获取产品列表失败</p>';
                    }
                })
                .catch(error => {
                    console.error('获取产品列表失败:', error);
                    resultsDiv.innerHTML = '<p style="color: red;">请求失败: ' + error.message + '</p>';
                    document.getElementById('json-response').textContent = '错误: ' + error.message;
                });
        }

        function testProductImages(productId) {
            if (!productId) {
                productId = document.getElementById('productId').value;
            }
            
            if (!productId) {
                alert('请输入产品ID');
                return;
            }

            const resultsDiv = document.getElementById('images-result');
            resultsDiv.innerHTML = '<p>加载中...</p>';

            axios.get(`/products/${productId}/images`)
                .then(response => {
                    console.log('产品图片响应:', response.data);
                    document.getElementById('json-response').textContent = JSON.stringify(response.data, null, 2);

                    if (response.data.success) {
                        const images = response.data.data || [];
                        
                        let html = `<h3>产品 ${productId} 的图片 (共 ${images.length} 张):</h3>`;
                        
                        if (images.length > 0) {
                            html += '<div class="image-grid">';
                            images.forEach((image, index) => {
                                html += `
                                    <div class="image-item">
                                        <div><strong>图片 ${index + 1}</strong></div>
                                        <div>ID: ${image.id}</div>
                                        <div>名称: ${image.imageName || '未命名'}</div>
                                        <div>主图: ${image.isPrimary ? '是' : '否'} ${image.isPrimary ? '<span class="primary-badge">主图</span>' : ''}</div>
                                        <img src="${image.imageUrl}" alt="${image.imageName || '产品图片'}" 
                                             onload="showStatus(this, 'success')" 
                                             onerror="showStatus(this, 'error')">
                                        <div class="url" style="font-size: 11px; color: #666; margin-top: 5px;">${image.imageUrl}</div>
                                        <div class="status">加载中...</div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                        } else {
                            html += '<p style="color: #666;">该产品暂无图片</p>';
                        }
                        
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = '<p style="color: red;">获取图片失败: ' + (response.data.message || '未知错误') + '</p>';
                    }
                })
                .catch(error => {
                    console.error('获取产品图片失败:', error);
                    resultsDiv.innerHTML = '<p style="color: red;">请求失败: ' + error.message + '</p>';
                    document.getElementById('json-response').textContent = '错误: ' + error.message;
                });
        }

        // 页面加载时自动获取产品列表
        window.onload = function() {
            loadProducts();
        };
    </script>
</body>
</html>
