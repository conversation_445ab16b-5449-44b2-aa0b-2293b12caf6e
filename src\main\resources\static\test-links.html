<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分类测试链接</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .link-group { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .link { display: block; margin: 10px 0; padding: 10px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .link:hover { background: #0056b3; }
        .description { color: #666; margin-bottom: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 分类功能测试链接</h1>
        
        <div class="link-group">
            <h3>📊 调试和测试页面</h3>
            <div class="description">用于检查分类数据和API状态</div>
            <a href="/quick-test.html" class="link">快速API测试</a>
            <a href="/debug-categories.html" class="link">完整调试工具</a>
            <a href="/test-categories.html" class="link">分类功能测试</a>
        </div>

        <div class="link-group">
            <h3>🏠 主页面测试</h3>
            <div class="description">测试主页面的分类功能</div>
            <a href="/index.html" class="link">正常模式 (使用API分类)</a>
            <a href="/index.html?useDefault=true" class="link">测试模式 (使用默认分类)</a>
        </div>

        <div class="link-group">
            <h3>🔍 直接API测试</h3>
            <div class="description">直接访问API接口</div>
            <a href="/admin/category/all" class="link">分类API</a>
            <a href="/products/category/0" class="link">全部产品API</a>
            <a href="/products/category/1" class="link">分类1产品API</a>
            <a href="/fix/actual-categories" class="link">实际分类数据API</a>
        </div>

        <div class="link-group">
            <h3>📝 说明</h3>
            <div class="description">
                <p><strong>问题现象:</strong> 所有分类显示的ID都是0，导致都显示全部产品</p>
                <p><strong>测试步骤:</strong></p>
                <ol>
                    <li>先访问"快速API测试"检查分类API是否正常</li>
                    <li>访问"测试模式"查看使用默认分类是否能正常工作</li>
                    <li>如果测试模式正常，说明问题在于分类数据加载</li>
                    <li>使用"完整调试工具"进行深度分析</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
