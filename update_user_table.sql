-- 更新用户表结构，添加新字段
-- 如果表不存在id字段，添加自增主键
ALTER TABLE user ADD COLUMN id BIGINT AUTO_INCREMENT PRIMARY KEY FIRST;

-- 添加头像字段
ALTER TABLE user ADD COLUMN avatar VARCHAR(255) DEFAULT '/images/avatar/default-avatar.jpg' COMMENT '用户头像';

-- 添加签名字段
ALTER TABLE user ADD COLUMN signature VARCHAR(500) DEFAULT '' COMMENT '用户签名';

-- 添加创建时间字段
ALTER TABLE user ADD COLUMN created_time BIGINT DEFAULT 0 COMMENT '创建时间';

-- 更新现有数据的created_time字段
UPDATE user SET created_time = register_time WHERE created_time = 0;

-- 修改user_id字段为非主键（如果之前是主键的话）
-- ALTER TABLE user DROP PRIMARY KEY;
-- ALTER TABLE user ADD PRIMARY KEY (id);

-- 为user_id字段添加唯一索引
ALTER TABLE user ADD UNIQUE INDEX idx_user_id (user_id);

-- 为phone字段添加唯一索引
ALTER TABLE user ADD UNIQUE INDEX idx_phone (phone);
