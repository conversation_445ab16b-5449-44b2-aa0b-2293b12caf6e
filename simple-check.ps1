# 简单检查应用状态
Write-Host "🔍 检查SpringBoot应用状态..." -ForegroundColor Green

# 检查端口
$portCheck = netstat -an | findstr ":8082"
if ($portCheck) {
    Write-Host "✅ 端口8082有活动:" -ForegroundColor Green
    Write-Host $portCheck -ForegroundColor White
} else {
    Write-Host "❌ 端口8082无活动" -ForegroundColor Red
}

# 检查进程
$javaProcesses = Get-Process java -ErrorAction SilentlyContinue
if ($javaProcesses) {
    Write-Host "✅ Java进程正在运行:" -ForegroundColor Green
    $javaProcesses | Format-Table Id, ProcessName, CPU -AutoSize
} else {
    Write-Host "❌ 没有Java进程运行" -ForegroundColor Red
}

Write-Host "🌐 尝试访问: http://localhost:8082/index.html" -ForegroundColor Cyan
