package cn.gzsf.javawebspringboot.controller;

import cn.gzsf.javawebspringboot.dto.ProductRequest;
import cn.gzsf.javawebspringboot.entity.Product;
import cn.gzsf.javawebspringboot.service.ProductCategoryService;
import cn.gzsf.javawebspringboot.service.ProductService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

// 控制器类，处理产品相关的 HTTP 请求
@RestController
public class ProductController {
    @Autowired
    private ProductService productService;

    @Autowired
    private ProductCategoryService productCategoryService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Value("${file.upload.dir}")
    private String uploadDir;

    // 获取所有产品
    @GetMapping("/products")
    public List<Product> getAllProducts() {
        return productService.getAllProducts();
    }

    // 获取新品产品（用于息壤臻选区域）
    @GetMapping("/products/new")
    public Map<String, Object> getNewProducts(@RequestParam(defaultValue = "6") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Product> newProducts = productService.getNewProducts(limit);
            result.put("success", true);
            result.put("data", newProducts);
            result.put("message", "获取新品成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取新品失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 根据分类获取产品
    @GetMapping("/products/category/{categoryId}")
    public Map<String, Object> getProductsByCategory(@PathVariable Long categoryId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Product> products;
            if (categoryId == 0) {
                // categoryId=0表示获取所有产品
                products = productService.getAllProducts();
            } else {
                products = productService.getProductsByCategory(categoryId);
            }
            result.put("success", true);
            result.put("data", products);
            result.put("message", "获取产品成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取产品失败: " + e.getMessage());
            e.printStackTrace();
        }
        return result;
    }

    // 获取单个产品详情
    @GetMapping("/api/products/{id}")
    public Map<String, Object> getProductById(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询产品基本信息
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "COALESCE(pi.image_url, p.image_url) as image_url " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                "WHERE p.id = ?";

            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, id);

            if (products.isEmpty()) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            Map<String, Object> product = products.get(0);

            // 获取产品分类信息
            String categorySql = "SELECT c.id, c.name FROM categories c " +
                "JOIN product_category pc ON c.id = pc.category_id " +
                "WHERE pc.product_id = ?";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categorySql, id);

            // 添加分类信息到产品数据
            product.put("categories", categories);

            result.put("success", true);
            result.put("data", product);
            result.put("message", "获取产品详情成功");

            System.out.println("📦 获取产品详情成功: ID=" + id + ", 名称=" + product.get("name"));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取产品详情失败: " + e.getMessage());
        }

        return result;
    }

    // 分页获取产品列表（带主图和搜索功能）
    @GetMapping("/products/page")
    public Map<String, Object> getProductsPage(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 构建WHERE条件
            StringBuilder whereClause = new StringBuilder();
            List<Object> params = new ArrayList<>();

            if (name != null && !name.trim().isEmpty()) {
                whereClause.append(" WHERE p.name LIKE ?");
                params.add("%" + name.trim() + "%");
            }

            if (category != null && !category.trim().isEmpty()) {
                try {
                    // 尝试将category解析为ID
                    Long categoryId = Long.parseLong(category.trim());
                    if (whereClause.length() > 0) {
                        whereClause.append(" AND EXISTS (SELECT 1 FROM product_category pc WHERE pc.product_id = p.id AND pc.category_id = ?)");
                    } else {
                        whereClause.append(" WHERE EXISTS (SELECT 1 FROM product_category pc WHERE pc.product_id = p.id AND pc.category_id = ?)");
                    }
                    params.add(categoryId);
                } catch (NumberFormatException e) {
                    // 如果不是数字，则按分类名称搜索
                    if (whereClause.length() > 0) {
                        whereClause.append(" AND EXISTS (SELECT 1 FROM product_category pc JOIN categories c ON pc.category_id = c.id WHERE pc.product_id = p.id AND c.name LIKE ?)");
                    } else {
                        whereClause.append(" WHERE EXISTS (SELECT 1 FROM product_category pc JOIN categories c ON pc.category_id = c.id WHERE pc.product_id = p.id AND c.name LIKE ?)");
                    }
                    params.add("%" + category.trim() + "%");
                }
            }

            // 直接从数据库获取带主图的产品数据
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "COALESCE(pi.image_url, p.image_url) as primary_image_url " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                whereClause.toString() + " " +
                "ORDER BY p.id DESC " +
                "LIMIT ?, ?";

            // 添加分页参数
            params.add(offset);
            params.add(size);

            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, params.toArray());

            // 获取总数（应用相同的搜索条件）
            StringBuilder countWhereClause = new StringBuilder();
            List<Object> countParams = new ArrayList<>();

            if (name != null && !name.trim().isEmpty()) {
                countWhereClause.append(" WHERE p.name LIKE ?");
                countParams.add("%" + name.trim() + "%");
            }

            if (category != null && !category.trim().isEmpty()) {
                try {
                    // 尝试将category解析为ID
                    Long categoryId = Long.parseLong(category.trim());
                    if (countWhereClause.length() > 0) {
                        countWhereClause.append(" AND EXISTS (SELECT 1 FROM product_category pc WHERE pc.product_id = p.id AND pc.category_id = ?)");
                    } else {
                        countWhereClause.append(" WHERE EXISTS (SELECT 1 FROM product_category pc WHERE pc.product_id = p.id AND pc.category_id = ?)");
                    }
                    countParams.add(categoryId);
                } catch (NumberFormatException e) {
                    // 如果不是数字，则按分类名称搜索
                    if (countWhereClause.length() > 0) {
                        countWhereClause.append(" AND EXISTS (SELECT 1 FROM product_category pc JOIN categories c ON pc.category_id = c.id WHERE pc.product_id = p.id AND c.name LIKE ?)");
                    } else {
                        countWhereClause.append(" WHERE EXISTS (SELECT 1 FROM product_category pc JOIN categories c ON pc.category_id = c.id WHERE pc.product_id = p.id AND c.name LIKE ?)");
                    }
                    countParams.add("%" + category.trim() + "%");
                }
            }

            String countSql = "SELECT COUNT(*) FROM products p" + countWhereClause.toString();
            int total = jdbcTemplate.queryForObject(countSql, Integer.class, countParams.toArray());

            // 转换为Product对象格式
            List<Map<String, Object>> productList = new ArrayList<>();
            for (Map<String, Object> row : products) {
                Map<String, Object> product = new HashMap<>();
                product.put("id", row.get("id"));
                product.put("name", row.get("name"));
                product.put("description", row.get("description"));
                product.put("price", row.get("price"));
                product.put("stock", row.get("stock"));
                product.put("imageUrl", row.get("primary_image_url")); // 使用主图URL
                product.put("isNew", row.get("is_new"));
                productList.add(product);
            }

            result.put("success", true);
            result.put("data", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取产品列表失败: " + e.getMessage());
            // 如果出错，回退到原来的方式
            try {
                int offset = (page - 1) * size;
                List<Product> products = productService.getProductsPage(offset, size);
                int total = productService.getTotalProductCount();
                result.put("success", true);
                result.put("data", products);
                result.put("total", total);
                result.put("page", page);
                result.put("size", size);
                result.put("totalPages", (int) Math.ceil((double) total / size));
            } catch (Exception e2) {
                result.put("success", false);
                result.put("data", new ArrayList<>());
                result.put("total", 0);
            }
        }

        return result;
    }

    // 强制刷新产品分页数据 - 绕过缓存
    @GetMapping("/products/page/refresh")
    public Map<String, Object> getProductsPageRefresh(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 计算偏移量
            int offset = (page - 1) * size;

            // 直接从数据库获取最新数据，包含主图信息
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "COALESCE(pi.image_url, p.image_url) as primary_image_url " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                "ORDER BY p.id DESC " +
                "LIMIT ?, ?";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, offset, size);

            String countSql = "SELECT COUNT(*) FROM products";
            int total = jdbcTemplate.queryForObject(countSql, Integer.class);

            // 转换为Product对象格式
            List<Map<String, Object>> productList = new ArrayList<>();
            for (Map<String, Object> row : products) {
                Map<String, Object> product = new HashMap<>();
                product.put("id", row.get("id"));
                product.put("name", row.get("name"));
                product.put("description", row.get("description"));
                product.put("price", row.get("price"));
                product.put("stock", row.get("stock"));
                product.put("imageUrl", row.get("primary_image_url")); // 使用主图URL
                product.put("isNew", row.get("is_new"));
                productList.add(product);

                // 调试输出
                System.out.println("产品 " + row.get("id") + " 的主图URL: " + row.get("primary_image_url"));
            }

            result.put("success", true);
            result.put("data", productList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("totalPages", (int) Math.ceil((double) total / size));
            result.put("refreshed", true);
            result.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("message", "获取产品列表失败: " + e.getMessage());
            result.put("data", new ArrayList<>());
            result.put("total", 0);
        }

        return result;
    }

    // 获取产品的分类信息
    @GetMapping("/products/{id}/categories")
    public Map<String, Object> getProductCategories(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Long> categoryIds = productCategoryService.getProductCategoryIds(id);
            List<String> categoryNames = productCategoryService.getProductCategoryNames(id);

            result.put("success", true);
            result.put("categoryIds", categoryIds);
            result.put("categoryNames", categoryNames);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        return result;
    }

    // 根据关键词搜索产品
    @GetMapping("/products/search")
    public List<Map<String, Object>> searchProducts(@RequestParam("keyword") String keyword) {
        try {
            // 使用SQL查询搜索产品，包含主图信息
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "COALESCE(pi.image_url, p.image_url) as imageUrl " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                "WHERE p.name LIKE ? OR p.description LIKE ? " +
                "ORDER BY p.id DESC";

            String searchKeyword = "%" + keyword + "%";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, searchKeyword, searchKeyword);

            System.out.println("🔍 搜索关键词: " + keyword + ", 找到 " + products.size() + " 个产品");

            return products;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    // 获取新品产品（用于首页息壤臻选区域）
    @GetMapping("/products/new")
    public Map<String, Object> getNewProducts(@RequestParam(defaultValue = "6") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 查询标记为新品的产品，带主图
            String sql = "SELECT " +
                "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                "COALESCE(pi.image_url, p.image_url) as imageUrl " +
                "FROM products p " +
                "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                "WHERE p.is_new = 1 " +
                "ORDER BY p.id DESC " +
                "LIMIT ?";

            List<Map<String, Object>> products = jdbcTemplate.queryForList(sql, limit);

            result.put("success", true);
            result.put("data", products);
            result.put("count", products.size());
            result.put("message", "获取新品成功");

            System.out.println("🆕 获取新品产品成功，共 " + products.size() + " 个");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取新品失败: " + e.getMessage());
        }
        return result;
    }

    // 根据分类获取产品（用于分类导航）
    @GetMapping("/products/category/{categoryId}")
    public Map<String, Object> getProductsByCategory(@PathVariable Long categoryId,
                                                   @RequestParam(defaultValue = "12") int limit) {
        Map<String, Object> result = new HashMap<>();
        try {
            String sql;
            List<Map<String, Object>> products;

            if (categoryId == 0) { // categoryId为0表示"全部"
                // 获取所有产品
                sql = "SELECT " +
                    "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                    "COALESCE(pi.image_url, p.image_url) as imageUrl " +
                    "FROM products p " +
                    "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                    "ORDER BY p.id DESC " +
                    "LIMIT ?";
                products = jdbcTemplate.queryForList(sql, limit);
            } else {
                // 获取指定分类的产品
                sql = "SELECT " +
                    "p.id, p.name, p.description, p.price, p.stock, p.is_new, " +
                    "COALESCE(pi.image_url, p.image_url) as imageUrl " +
                    "FROM products p " +
                    "LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1 " +
                    "JOIN product_category pc ON p.id = pc.product_id " +
                    "WHERE pc.category_id = ? " +
                    "ORDER BY p.id DESC " +
                    "LIMIT ?";
                products = jdbcTemplate.queryForList(sql, categoryId, limit);
            }

            result.put("success", true);
            result.put("data", products);
            result.put("count", products.size());
            result.put("categoryId", categoryId);
            result.put("message", "获取分类产品成功");

            System.out.println("📂 获取分类 " + categoryId + " 的产品成功，共 " + products.size() + " 个");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取分类产品失败: " + e.getMessage());
        }
        return result;
    }

    // 批量设置产品为新品（用于测试）
    @PostMapping("/products/set-new")
    public Map<String, Object> setProductsAsNew(@RequestParam(defaultValue = "6") int count) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 先清除所有新品标记
            String clearSql = "UPDATE products SET is_new = 0";
            jdbcTemplate.update(clearSql);

            // 将最新的几个产品设置为新品
            String setSql = "UPDATE products SET is_new = 1 WHERE id IN (" +
                "SELECT id FROM (SELECT id FROM products ORDER BY id DESC LIMIT ?) AS temp)";
            int updated = jdbcTemplate.update(setSql, count);

            result.put("success", true);
            result.put("updated", updated);
            result.put("message", "成功设置 " + updated + " 个产品为新品");

            System.out.println("🆕 批量设置新品成功，共 " + updated + " 个产品");

        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "设置新品失败: " + e.getMessage());
        }
        return result;
    }

    // 编辑产品接口 - 支持分类
    @PostMapping("/products/updateProduct")
    public Map<String, Object> updateProduct(@RequestBody ProductRequest productRequest) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建Product对象
            Product product = new Product();
            product.setId(productRequest.getId());
            product.setName(productRequest.getName());
            product.setDescription(productRequest.getDescription());
            product.setPrice(productRequest.getPrice());
            product.setStock(productRequest.getStock());
            product.setImageUrl(productRequest.getImageUrl());
            product.setNew(productRequest.getIsNew() != null ? productRequest.getIsNew() : false);

            // 更新产品信息
            boolean productResult = productService.updateProduct(product);

            if (productResult) {
                // 直接使用JdbcTemplate更新产品分类关联
                System.out.println("🔄 开始更新产品分类关联: 产品ID=" + product.getId() + ", 分类IDs=" + productRequest.getCategoryIds());

                try {
                    // 先删除现有的分类关联
                    String deleteSql = "DELETE FROM product_category WHERE product_id = ?";
                    int deletedRows = jdbcTemplate.update(deleteSql, product.getId());
                    System.out.println("🗑️ 删除了 " + deletedRows + " 条现有分类关联");

                    // 插入新的分类关联
                    List<Long> categoryIds = productRequest.getCategoryIds();
                    int insertedRows = 0;

                    if (categoryIds != null && !categoryIds.isEmpty()) {
                        for (Long categoryId : categoryIds) {
                            // 先尝试简单的插入（只有product_id和category_id）
                            try {
                                String insertSql = "INSERT IGNORE INTO product_category (product_id, category_id) VALUES (?, ?)";
                                jdbcTemplate.update(insertSql, product.getId(), categoryId);
                                insertedRows++;
                                System.out.println("✅ 插入分类关联: 产品" + product.getId() + " -> 分类" + categoryId);
                            } catch (Exception e) {
                                // 如果失败，尝试带created_time的插入
                                try {
                                    long currentTime = System.currentTimeMillis();
                                    String insertSql2 = "INSERT IGNORE INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                                    jdbcTemplate.update(insertSql2, product.getId(), categoryId, currentTime);
                                    insertedRows++;
                                    System.out.println("✅ 插入分类关联(带时间): 产品" + product.getId() + " -> 分类" + categoryId);
                                } catch (Exception e2) {
                                    System.err.println("❌ 插入分类关联失败: " + e2.getMessage());
                                }
                            }
                        }
                    }

                    result.put("success", true);
                    result.put("message", "更新成功");
                    result.put("deletedCategoryRows", deletedRows);
                    result.put("insertedCategoryRows", insertedRows);
                    System.out.println("✅ 成功更新产品: " + product.getName() + ", ID: " + product.getId() + ", 分类关联: " + insertedRows + " 条");

                } catch (Exception e) {
                    result.put("success", false);
                    result.put("message", "更新产品成功，但分类关联更新异常: " + e.getMessage());
                    System.err.println("❌ 分类关联更新异常: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                result.put("success", false);
                result.put("message", "更新失败，可能是数据未发生变化或更新操作未生效");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新产品信息时失败，服务器内部错误，请稍后重试");
        }
        return result;
    }

    // 新建产品接口 - 支持分类
    @PostMapping("/products/add")
    public Map<String, Object> addProduct(@RequestBody ProductRequest productRequest) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 创建Product对象
            Product product = new Product();
            product.setName(productRequest.getName());
            product.setDescription(productRequest.getDescription());
            product.setPrice(productRequest.getPrice());
            product.setStock(productRequest.getStock());
            product.setImageUrl(productRequest.getImageUrl());
            product.setNew(productRequest.getIsNew() != null ? productRequest.getIsNew() : false);

            // 保存产品信息
            boolean productResult = productService.addProduct(product);

            if (productResult && product.getId() != null) {
                // 直接使用JdbcTemplate保存产品分类关联
                try {
                    List<Long> categoryIds = productRequest.getCategoryIds();
                    int insertedRows = 0;

                    if (categoryIds != null && !categoryIds.isEmpty()) {
                        for (Long categoryId : categoryIds) {
                            // 先尝试简单的插入（只有product_id和category_id）
                            try {
                                String insertSql = "INSERT IGNORE INTO product_category (product_id, category_id) VALUES (?, ?)";
                                jdbcTemplate.update(insertSql, product.getId(), categoryId);
                                insertedRows++;
                                System.out.println("✅ 新增产品分类关联: 产品" + product.getId() + " -> 分类" + categoryId);
                            } catch (Exception e) {
                                // 如果失败，尝试带created_time的插入
                                try {
                                    long currentTime = System.currentTimeMillis();
                                    String insertSql2 = "INSERT IGNORE INTO product_category (product_id, category_id, created_time) VALUES (?, ?, ?)";
                                    jdbcTemplate.update(insertSql2, product.getId(), categoryId, currentTime);
                                    insertedRows++;
                                    System.out.println("✅ 新增产品分类关联(带时间): 产品" + product.getId() + " -> 分类" + categoryId);
                                } catch (Exception e2) {
                                    System.err.println("❌ 新增产品分类关联失败: " + e2.getMessage());
                                }
                            }
                        }
                    }

                    result.put("success", true);
                    result.put("message", "新增成功");
                    result.put("productId", product.getId());
                    result.put("insertedCategoryRows", insertedRows);
                    System.out.println("✅ 成功新增产品: " + product.getName() + ", ID: " + product.getId() + ", 分类关联: " + insertedRows + " 条");

                } catch (Exception e) {
                    System.err.println("❌ 保存产品分类关联失败: " + e.getMessage());
                    e.printStackTrace();

                    result.put("success", true); // 产品保存成功，只是分类关联失败
                    result.put("message", "新增产品成功，但分类关联保存失败: " + e.getMessage());
                    result.put("productId", product.getId());
                }
            } else {
                result.put("success", false);
                result.put("message", "新增产品失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "新增失败: " + e.getMessage());
        }
        return result;
    }

    // 删除产品接口
    @DeleteMapping("/products/{id}")
    public String deleteProduct(@PathVariable Long id) {
        try {
            boolean result = productService.deleteProduct(id);
            if (result) {
                return "删除成功";
            } else {
                return "删除失败";
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "删除失败";
        }
    }


    // 文件上传接口
    @PostMapping("/products/uploadImage")
    public Map<String, Object> uploadImage(@RequestParam("file") MultipartFile file,
                                         @RequestParam(value = "productId", required = false) Long productId,
                                         @RequestParam(value = "type", required = false) String type) {
        Map<String, Object> result = new HashMap<>();
        if (file.isEmpty()) {
            result.put("success", false);
            result.put("message", "上传的文件为空");
            return result;
        }

        // 获取文件扩展名
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));

        // 检查文件扩展名是否为 .jpg 或 .png
        if (!(".jpg".equalsIgnoreCase(fileExtension) || ".png".equalsIgnoreCase(fileExtension))) {
            result.put("success", false);
            result.put("message", "仅支持上传 .jpg 或 .png 格式的图片");
            return result;
        }

        // 生成唯一的文件名
        String uniqueFileName = UUID.randomUUID().toString() + fileExtension;

        try {
            // 检查 uploadDir 是否为 null
            if (uploadDir == null) {
                result.put("success", false);
                result.put("message", "文件上传目录配置为空，请检查 application.properties 文件");
                return result;
            }

            // 根据类型决定保存路径
            String targetDir;
            String imageUrl;

            if ("carousel".equals(type)) {
                // 轮播图保存到banner文件夹 - 使用绝对路径
                String currentDir = System.getProperty("user.dir");
                targetDir = currentDir + "/src/main/resources/static/images/banner";
                imageUrl = "/images/banner/" + uniqueFileName;
                System.out.println("🎠 轮播图上传，保存到banner文件夹: " + targetDir);
            } else {
                // 普通图片保存到配置的目录
                String currentDir = System.getProperty("user.dir");
                targetDir = currentDir + "/" + uploadDir;
                imageUrl = "/images/" + uniqueFileName;
                System.out.println("📸 普通图片上传，保存到配置目录: " + targetDir);
            }

            File imagesDir = new File(targetDir);
            if (!imagesDir.exists()) {
                boolean created = imagesDir.mkdirs();
                System.out.println("📁 创建上传目录: " + (created ? "成功" : "失败") + " - " + targetDir);
            }

            // 保存文件
            File targetFile = new File(imagesDir, uniqueFileName);
            file.transferTo(targetFile);

            System.out.println("📸 文件上传保存成功: " + targetFile.getAbsolutePath());
            if (productId != null) {
                // 从数据库中查询该产品的现有信息
                Product existingProduct = productService.getProductById(productId);
                if (existingProduct != null) {
                    // 更新 imageUrl 字段
                    existingProduct.setImageUrl(imageUrl);
                    // 更新数据库中的产品信息
                    boolean updateResult = productService.updateProduct(existingProduct);
                    if (updateResult) {
                        result.put("success", true);
                        result.put("imageUrl", imageUrl);
                    } else {
                        result.put("success", false);
                        result.put("message", "更新数据库失败");
                    }
                } else {
                    result.put("success", false);
                    result.put("message", "未找到该产品");
                }
            } else {
                result.put("success", true);
                result.put("imageUrl", imageUrl);
            }
        } catch (IOException e) {
            e.printStackTrace(); // 打印详细的错误信息
            result.put("success", false);
            result.put("message", "文件保存失败: " + e.getMessage());
        }
        return result;
    }






    /**
     * 设置产品主图
     */
    @PostMapping("/products/{productId}/set-primary-image")
    public Map<String, Object> setPrimaryImage(@PathVariable Long productId, @RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();

        try {
            String imageUrl = request.get("imageUrl");

            if (imageUrl == null || imageUrl.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "图片URL不能为空");
                return result;
            }

            System.out.println("🔄 设置产品主图: 产品ID=" + productId + ", 图片URL=" + imageUrl);

            // 1. 先将该产品的所有图片设为非主图
            String clearPrimarySql = "UPDATE product_images SET is_primary = 0 WHERE product_id = ?";
            jdbcTemplate.update(clearPrimarySql, productId);

            // 2. 设置指定图片为主图
            String setPrimarySql = "UPDATE product_images SET is_primary = 1 WHERE product_id = ? AND image_url = ?";
            int updatedRows = jdbcTemplate.update(setPrimarySql, productId, imageUrl);

            if (updatedRows > 0) {
                // 3. 同步更新products表的image_url字段
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, imageUrl, productId);

                result.put("success", true);
                result.put("message", "主图设置成功");
                result.put("imageUrl", imageUrl);
                System.out.println("✅ 成功设置产品 " + productId + " 的主图: " + imageUrl);
            } else {
                result.put("success", false);
                result.put("message", "图片不存在或设置失败");
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "设置主图失败: " + e.getMessage());
            System.err.println("❌ 设置主图失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 产品图片上传接口（新增产品专用）
     */
    @PostMapping("/products/upload-image")
    public Map<String, Object> uploadProductImage(
            @RequestParam("file") MultipartFile file,
            @RequestParam("productId") Long productId,
            @RequestParam(value = "imageName", required = false) String imageName,
            @RequestParam(value = "isPrimary", defaultValue = "0") String isPrimary,
            @RequestParam(value = "sortOrder", defaultValue = "0") String sortOrder) {

        Map<String, Object> result = new HashMap<>();

        try {
            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "上传的文件为空");
                return result;
            }

            // 检查文件大小（10MB = 10 * 1024 * 1024 bytes）
            long maxFileSize = 10 * 1024 * 1024; // 10MB
            if (file.getSize() > maxFileSize) {
                result.put("success", false);
                result.put("message", "文件大小不能超过 10MB");
                return result;
            }

            // 获取文件扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));

            // 检查文件扩展名
            if (!(".jpg".equalsIgnoreCase(fileExtension) || ".png".equalsIgnoreCase(fileExtension))) {
                result.put("success", false);
                result.put("message", "仅支持上传 .jpg 或 .png 格式的图片");
                return result;
            }

            // 生成唯一的文件名
            String uniqueFileName = UUID.randomUUID().toString() + fileExtension;

            // 检查上传目录
            if (uploadDir == null) {
                result.put("success", false);
                result.put("message", "文件上传目录配置为空");
                return result;
            }

            // 获取绝对路径
            String currentDir = System.getProperty("user.dir");
            String absoluteUploadDir = currentDir + "/" + uploadDir;
            File imagesDir = new File(absoluteUploadDir);

            if (!imagesDir.exists()) {
                boolean created = imagesDir.mkdirs();
                System.out.println("📁 创建产品图片目录: " + (created ? "成功" : "失败") + " - " + absoluteUploadDir);
            }

            // 保存文件
            File targetFile = new File(imagesDir, uniqueFileName);
            file.transferTo(targetFile);

            System.out.println("📸 产品图片上传保存成功: " + targetFile.getAbsolutePath());

            String imageUrl = "/images/" + uniqueFileName;

            // 保存到product_images表
            String insertSql = "INSERT INTO product_images (product_id, image_url, image_name, is_primary, sort_order, created_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?)";

            // 如果是主图，先清除其他主图
            boolean isPrimaryBool = "1".equals(isPrimary);
            if (isPrimaryBool) {
                String clearPrimarySql = "UPDATE product_images SET is_primary = 0 WHERE product_id = ?";
                jdbcTemplate.update(clearPrimarySql, productId);

                // 同步更新products表的image_url
                String updateProductSql = "UPDATE products SET image_url = ? WHERE id = ?";
                jdbcTemplate.update(updateProductSql, imageUrl, productId);
            }

            // 获取当前时间戳
            long currentTime = System.currentTimeMillis();
            jdbcTemplate.update(insertSql, productId, imageUrl, imageName, isPrimaryBool ? 1 : 0, Integer.parseInt(sortOrder), currentTime, currentTime);

            result.put("success", true);
            result.put("message", "图片上传成功");
            result.put("imageUrl", imageUrl);
            result.put("imageName", imageName);
            result.put("isPrimary", isPrimaryBool);

            System.out.println("✅ 产品图片上传成功: 产品ID=" + productId + ", 图片=" + imageUrl + ", 主图=" + isPrimaryBool);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "图片上传失败: " + e.getMessage());
            System.err.println("❌ 产品图片上传失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 获取产品的所有图片
     */
    @GetMapping("/products/{productId}/images")
    public Map<String, Object> getProductImages(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();

        try {
            String sql = "SELECT id, image_url, image_name, is_primary, sort_order FROM product_images WHERE product_id = ? ORDER BY sort_order, id";
            List<Map<String, Object>> rawImages = jdbcTemplate.queryForList(sql, productId);

            // 转换数据格式，将下划线格式转换为驼峰格式
            List<Map<String, Object>> images = new ArrayList<>();
            for (Map<String, Object> rawImage : rawImages) {
                Map<String, Object> image = new HashMap<>();
                image.put("id", rawImage.get("id"));
                image.put("imageUrl", rawImage.get("image_url"));
                image.put("imageName", rawImage.get("image_name"));
                image.put("isPrimary", Boolean.TRUE.equals(rawImage.get("is_primary")) || Integer.valueOf(1).equals(rawImage.get("is_primary")));
                image.put("sortOrder", rawImage.get("sort_order"));
                images.add(image);
            }

            result.put("success", true);
            result.put("data", images);
            result.put("totalImages", images.size());

            System.out.println("📋 获取产品图片成功: 产品ID=" + productId + ", 图片数量=" + images.size());

            // 调试输出
            if (!images.isEmpty()) {
                System.out.println("🖼️ 图片列表:");
                for (int i = 0; i < images.size(); i++) {
                    Map<String, Object> img = images.get(i);
                    System.out.println("  图片" + (i + 1) + ": ID=" + img.get("id") +
                                     ", URL=" + img.get("imageUrl") +
                                     ", 主图=" + img.get("isPrimary") +
                                     ", 名称=" + img.get("imageName"));
                }
            }

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取产品图片失败: " + e.getMessage());
            System.err.println("❌ 获取产品图片失败: " + e.getMessage());
            e.printStackTrace();
        }

        return result;
    }

    // 切换产品新品状态
    @PostMapping("/products/{productId}/toggle-new")
    public Map<String, Object> toggleProductNewStatus(@PathVariable Long productId, @RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Boolean isNew = (Boolean) request.get("isNew");

            // 更新产品新品状态
            String sql = "UPDATE products SET is_new = ? WHERE id = ?";
            int updated = jdbcTemplate.update(sql, isNew, productId);

            if (updated > 0) {
                result.put("success", true);
                result.put("message", "新品状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "产品不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    // 切换产品上下架状态
    @PostMapping("/products/{productId}/toggle-status")
    public Map<String, Object> toggleProductStatus(@PathVariable Long productId, @RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            Integer status = (Integer) request.get("status");

            // 更新产品状态
            String sql = "UPDATE products SET status = ? WHERE id = ?";
            int updated = jdbcTemplate.update(sql, status, productId);

            if (updated > 0) {
                result.put("success", true);
                result.put("message", "产品状态更新成功");
            } else {
                result.put("success", false);
                result.put("message", "产品不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    // 复制产品
    @PostMapping("/products/copy")
    public Map<String, Object> copyProduct(@RequestBody Map<String, Object> productData) {
        Map<String, Object> result = new HashMap<>();
        try {
            String name = (String) productData.get("name");
            String description = (String) productData.get("description");
            Double price = Double.valueOf(productData.get("price").toString());
            Integer stock = Integer.valueOf(productData.get("stock").toString());
            String imageUrl = (String) productData.get("imageUrl");
            Boolean isNew = (Boolean) productData.get("isNew");

            // 插入复制的产品
            String sql = "INSERT INTO products (name, description, price, stock, image_url, is_new, status) VALUES (?, ?, ?, ?, ?, ?, 1)";
            int inserted = jdbcTemplate.update(sql, name, description, price, stock, imageUrl, isNew);

            if (inserted > 0) {
                result.put("success", true);
                result.put("message", "产品复制成功");
            } else {
                result.put("success", false);
                result.put("message", "复制失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "复制失败: " + e.getMessage());
        }
        return result;
    }

    // 批量删除产品
    @PostMapping("/products/batch-delete")
    public Map<String, Object> batchDeleteProducts(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        try {
            @SuppressWarnings("unchecked")
            List<Integer> productIds = (List<Integer>) request.get("productIds");

            if (productIds == null || productIds.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要删除的产品");
                return result;
            }

            // 构建删除SQL
            String placeholders = String.join(",", productIds.stream().map(id -> "?").toArray(String[]::new));
            String sql = "DELETE FROM products WHERE id IN (" + placeholders + ")";

            int deleted = jdbcTemplate.update(sql, productIds.toArray());

            result.put("success", true);
            result.put("message", "成功删除 " + deleted + " 个产品");
            result.put("deletedCount", deleted);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "批量删除失败: " + e.getMessage());
        }
        return result;
    }

    // 获取产品详情（包括图片和分类）
    @GetMapping("/products/detail/{productId}")
    public Map<String, Object> getProductDetail(@PathVariable Long productId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取产品基本信息
            String productSql = "SELECT * FROM products WHERE id = ?";
            List<Map<String, Object>> products = jdbcTemplate.queryForList(productSql, productId);

            if (products.isEmpty()) {
                result.put("success", false);
                result.put("message", "产品不存在");
                return result;
            }

            Map<String, Object> product = products.get(0);

            // 获取产品图片
            String imagesSql = "SELECT * FROM product_images WHERE product_id = ? ORDER BY is_primary DESC, id ASC";
            List<Map<String, Object>> images = jdbcTemplate.queryForList(imagesSql, productId);
            product.put("images", images);

            // 获取产品分类
            String categorySql = "SELECT c.* FROM categories c " +
                               "JOIN product_category pc ON c.id = pc.category_id " +
                               "WHERE pc.product_id = ?";
            List<Map<String, Object>> categories = jdbcTemplate.queryForList(categorySql, productId);
            product.put("categories", categories);

            result.put("success", true);
            result.put("data", product);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "获取产品详情失败: " + e.getMessage());
        }
        return result;
    }
}