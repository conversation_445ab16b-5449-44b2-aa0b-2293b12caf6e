-- 创建产品收藏表
CREATE TABLE IF NOT EXISTS product_favorite (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    product_id BIGINT NOT NULL COMMENT '产品ID',
    created_time BIGINT NOT NULL COMMENT '创建时间',
    updated_time BIGINT NOT NULL COMMENT '更新时间',
    INDEX idx_user_phone (user_phone),
    INDEX idx_product_id (product_id),
    UNIQUE KEY uk_user_product (user_phone, product_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品收藏表';

-- 创建产品评论表
CREATE TABLE IF NOT EXISTS product_comment (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT NOT NULL COMMENT '产品ID',
    user_phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    user_name VARCHAR(50) COMMENT '用户名称',
    user_avatar VARCHAR(255) COMMENT '用户头像',
    content TEXT NOT NULL COMMENT '评论内容',
    rating INT DEFAULT 5 COMMENT '评分(1-5星)',
    images TEXT COMMENT '评论图片，多个图片用逗号分隔',
    created_time BIGINT NOT NULL COMMENT '创建时间',
    updated_time BIGINT NOT NULL COMMENT '更新时间',
    status INT DEFAULT 0 COMMENT '状态：0-待审核，1-已通过，2-已拒绝',
    INDEX idx_product_id (product_id),
    INDEX idx_user_phone (user_phone),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品评论表';

-- 创建产品分享表
CREATE TABLE IF NOT EXISTS product_share (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT NOT NULL COMMENT '产品ID',
    user_phone VARCHAR(20) NOT NULL COMMENT '用户手机号',
    share_type VARCHAR(20) NOT NULL COMMENT '分享类型：wechat, weibo, qq, link等',
    share_url VARCHAR(500) COMMENT '分享链接',
    created_time BIGINT NOT NULL COMMENT '创建时间',
    INDEX idx_product_id (product_id),
    INDEX idx_user_phone (user_phone),
    INDEX idx_share_type (share_type),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品分享表';

-- 创建评论图片目录
-- 注意：这个需要在文件系统中手动创建
-- mkdir -p src/main/resources/static/images/comments

-- 插入一些测试数据
INSERT INTO product_favorite (user_phone, product_id, created_time, updated_time) VALUES
('13800138001', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138001', 2, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000),
('13800138002', 1, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000);

INSERT INTO product_comment (product_id, user_phone, user_name, user_avatar, content, rating, created_time, updated_time, status) VALUES
(1, '13800138001', '测试用户1', '/images/avatars/default-avatar.jpg', '这个产品非常好用，推荐大家购买！', 5, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1),
(1, '13800138002', '测试用户2', '/images/avatars/default-avatar.jpg', '质量不错，物流很快。', 4, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1),
(2, '13800138001', '测试用户1', '/images/avatars/default-avatar.jpg', '包装精美，产品质量很好。', 5, UNIX_TIMESTAMP() * 1000, UNIX_TIMESTAMP() * 1000, 1);

INSERT INTO product_share (product_id, user_phone, share_type, created_time) VALUES
(1, '13800138001', 'wechat', UNIX_TIMESTAMP() * 1000),
(1, '13800138002', 'weibo', UNIX_TIMESTAMP() * 1000),
(2, '13800138001', 'qq', UNIX_TIMESTAMP() * 1000);
