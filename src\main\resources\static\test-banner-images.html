<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner图片访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .image-test {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-test img {
            max-width: 300px;
            max-height: 200px;
            border-radius: 8px;
            margin: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .url {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Banner图片访问测试</h1>
        <p>测试banner文件夹中的图片是否可以正常访问</p>

        <div class="image-test">
            <h3>测试图片1</h3>
            <div class="url">/images/banner/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg</div>
            <img src="/images/banner/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg" 
                 alt="测试图片1" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status1">加载中...</span>
        </div>

        <div class="image-test">
            <h3>测试图片2</h3>
            <div class="url">/images/banner/496ea50b-83b6-438b-8493-2e49911b089e.jpg</div>
            <img src="/images/banner/496ea50b-83b6-438b-8493-2e49911b089e.jpg" 
                 alt="测试图片2" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status2">加载中...</span>
        </div>

        <div class="image-test">
            <h3>测试图片3</h3>
            <div class="url">/images/banner/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg</div>
            <img src="/images/banner/acd8ab1a-4e42-41c5-bca6-21d886257b1c.jpg" 
                 alt="测试图片3" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status3">加载中...</span>
        </div>

        <div class="image-test">
            <h3>上传的图片1</h3>
            <div class="url">/images/banner/6456371b-418c-4a13-beac-8a98540b7c18.jpg</div>
            <img src="/images/banner/6456371b-418c-4a13-beac-8a98540b7c18.jpg" 
                 alt="上传的图片1" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status4">加载中...</span>
        </div>

        <div class="image-test">
            <h3>上传的图片2</h3>
            <div class="url">/images/banner/5e7d4505-531c-434b-9c47-c7eee72fde52.png</div>
            <img src="/images/banner/5e7d4505-531c-434b-9c47-c7eee72fde52.png" 
                 alt="上传的图片2" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status5">加载中...</span>
        </div>

        <div class="image-test">
            <h3>测试普通images路径</h3>
            <div class="url">/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg</div>
            <img src="/images/074dc74f-ab1e-4241-ad36-40a3dae3234a.jpg" 
                 alt="普通images路径" 
                 onload="showStatus(this, 'success')" 
                 onerror="showStatus(this, 'error')">
            <span class="status" id="status6">加载中...</span>
        </div>

        <div style="margin-top: 30px;">
            <h3>📊 测试结果统计</h3>
            <div id="summary">等待图片加载完成...</div>
        </div>
    </div>

    <script>
        let loadedCount = 0;
        let errorCount = 0;
        let totalImages = 6;

        function showStatus(img, status) {
            const statusSpan = img.nextElementSibling;
            if (status === 'success') {
                statusSpan.textContent = '✅ 加载成功';
                statusSpan.className = 'status success';
                loadedCount++;
            } else {
                statusSpan.textContent = '❌ 加载失败';
                statusSpan.className = 'status error';
                errorCount++;
            }

            // 更新统计
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('summary');
            const processed = loadedCount + errorCount;
            
            if (processed === totalImages) {
                summary.innerHTML = `
                    <p><strong>测试完成！</strong></p>
                    <p>✅ 成功加载: ${loadedCount} 张</p>
                    <p>❌ 加载失败: ${errorCount} 张</p>
                    <p>📊 成功率: ${((loadedCount / totalImages) * 100).toFixed(1)}%</p>
                `;
                
                if (errorCount > 0) {
                    summary.innerHTML += '<p style="color: red;">⚠️ 有图片加载失败，请检查静态资源映射配置</p>';
                } else {
                    summary.innerHTML += '<p style="color: green;">🎉 所有图片加载成功！</p>';
                }
            } else {
                summary.innerHTML = `正在测试... (${processed}/${totalImages})`;
            }
        }

        // 页面加载完成后开始统计
        window.onload = function() {
            setTimeout(updateSummary, 1000);
        };
    </script>
</body>
</html>
