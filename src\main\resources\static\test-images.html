<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片访问测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        .image-item img {
            max-width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: 4px;
        }
        .image-item .url {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            word-break: break-all;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .product-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>图片访问测试页面</h1>
        
        <div class="test-section">
            <h2>1. 测试静态图片访问</h2>
            <p>测试几个已知存在的图片文件是否可以正常访问：</p>
            <div class="image-grid">
                <div class="image-item">
                    <img src="/images/01c0284a-5c57-4b76-a71c-3bb4ba327971.jpg" alt="测试图片1" 
                         onload="showStatus(this, 'success')" 
                         onerror="showStatus(this, 'error')">
                    <div class="url">/images/01c0284a-5c57-4b76-a71c-3bb4ba327971.jpg</div>
                    <div class="status" id="status-1">加载中...</div>
                </div>
                <div class="image-item">
                    <img src="/images/0968b834-8b08-4ee8-9815-d3614f8ae3b8.png" alt="测试图片2" 
                         onload="showStatus(this, 'success')" 
                         onerror="showStatus(this, 'error')">
                    <div class="url">/images/0968b834-8b08-4ee8-9815-d3614f8ae3b8.png</div>
                    <div class="status" id="status-2">加载中...</div>
                </div>
                <div class="image-item">
                    <img src="/images/avatar/admin.jpg" alt="测试图片3" 
                         onload="showStatus(this, 'success')" 
                         onerror="showStatus(this, 'error')">
                    <div class="url">/images/avatar/admin.jpg</div>
                    <div class="status" id="status-3">加载中...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>2. 产品数据测试</h2>
            <p>从API获取产品数据并测试图片显示：</p>
            <button onclick="loadProducts()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">加载产品数据</button>
            <div id="product-results"></div>
        </div>

        <div class="test-section">
            <h2>3. 直接URL测试</h2>
            <p>测试不同的URL格式：</p>
            <ul>
                <li><a href="/images/01c0284a-5c57-4b76-a71c-3bb4ba327971.jpg" target="_blank">直接访问图片1</a></li>
                <li><a href="/images/0968b834-8b08-4ee8-9815-d3614f8ae3b8.png" target="_blank">直接访问图片2</a></li>
                <li><a href="/images/avatar/admin.jpg" target="_blank">直接访问头像</a></li>
            </ul>
        </div>
    </div>

    <script>
        let statusCounter = 1;
        
        function showStatus(img, status) {
            const statusDiv = img.parentElement.querySelector('.status');
            if (status === 'success') {
                statusDiv.textContent = '✅ 加载成功';
                statusDiv.className = 'status success';
            } else {
                statusDiv.textContent = '❌ 加载失败';
                statusDiv.className = 'status error';
            }
        }

        function loadProducts() {
            const resultsDiv = document.getElementById('product-results');
            resultsDiv.innerHTML = '<p>正在加载产品数据...</p>';
            
            fetch('/products/page?page=1&size=5')
                .then(response => response.json())
                .then(data => {
                    console.log('产品数据:', data);
                    displayProducts(data.data || []);
                })
                .catch(error => {
                    console.error('加载产品失败:', error);
                    resultsDiv.innerHTML = '<p style="color: red;">加载产品数据失败: ' + error.message + '</p>';
                });
        }

        function displayProducts(products) {
            const resultsDiv = document.getElementById('product-results');
            
            if (products.length === 0) {
                resultsDiv.innerHTML = '<p>没有找到产品数据</p>';
                return;
            }

            let html = '<div class="image-grid">';
            products.forEach((product, index) => {
                const imageUrl = product.imageUrl || '/images/default-product.svg';
                html += `
                    <div class="image-item">
                        <div class="product-info">
                            <strong>ID:</strong> ${product.id}<br>
                            <strong>名称:</strong> ${product.name}<br>
                            <strong>价格:</strong> ¥${product.price}<br>
                            <strong>库存:</strong> ${product.stock || '未设置'}
                        </div>
                        <img src="${imageUrl}" alt="${product.name}" 
                             onload="showStatus(this, 'success')" 
                             onerror="showStatus(this, 'error')">
                        <div class="url">${imageUrl}</div>
                        <div class="status">加载中...</div>
                    </div>
                `;
            });
            html += '</div>';
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
