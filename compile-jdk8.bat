@echo off
echo ================================
echo Compiling with JDK 1.8
echo ================================

REM 设置JDK 1.8路径
set JAVA_HOME=D:\Program Files (x86)\JAVA\jdk
set PATH=%JAVA_HOME%\bin;%PATH%

echo JAVA_HOME: %JAVA_HOME%
echo.

echo Checking Java version...
java -version
echo.

echo Setting Maven environment...
set MAVEN_HOME=%cd%\apache-maven-3.9.4
set PATH=%MAVEN_HOME%\bin;%PATH%

echo Testing Maven...
mvn --version
echo.

echo Cleaning and compiling project...
mvn clean compile

echo.
echo Compilation completed!
echo Check target\classes for compiled files.

pause
