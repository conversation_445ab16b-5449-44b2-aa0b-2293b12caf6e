@echo off
echo ================================
echo Checking Port 8082
echo ================================

echo Checking if port 8082 is in use...
netstat -an | findstr :8082

echo.
echo Checking Java processes...
tasklist | findstr java

echo.
echo Trying to connect to localhost:8082...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8082' -TimeoutSec 5; Write-Host 'SUCCESS: Server is running'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host 'FAILED: Server not responding' }"

pause
