<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>分类映射测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .category-btn { background: #007bff; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer; }
        .category-btn:hover { background: #0056b3; }
        .category-btn.active { background: #28a745; }
        .result { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; font-family: monospace; }
        .product-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px; margin-top: 20px; }
        .product-card { border: 1px solid #ddd; padding: 10px; border-radius: 5px; background: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 分类映射功能测试</h1>
        
        <div>
            <h3>📋 分类按钮</h3>
            <button class="category-btn active" onclick="testCategory('all', 0)">全部</button>
            <button class="category-btn" onclick="testCategory('柔雾集', 1)">柔雾集</button>
            <button class="category-btn" onclick="testCategory('浅草藏', 2)">浅草藏</button>
            <button class="category-btn" onclick="testCategory('凛风匣', 3)">凛风匣</button>
            <button class="category-btn" onclick="testCategory('云栖盒', 4)">云栖盒</button>
            <button class="category-btn" onclick="testCategory('汲光瓶', 5)">汲光瓶</button>
        </div>

        <div>
            <h3>📊 测试结果</h3>
            <div id="result" class="result">点击分类按钮开始测试...</div>
        </div>

        <div>
            <h3>🛍️ 产品展示</h3>
            <div id="products" class="product-grid"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        let currentCategory = 'all';

        function showResult(content) {
            document.getElementById('result').textContent = content;
            console.log(content);
        }

        function showProducts(products) {
            const container = document.getElementById('products');
            
            if (!products || products.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666;">暂无产品</div>';
                return;
            }

            container.innerHTML = '';
            products.forEach(product => {
                const card = document.createElement('div');
                card.className = 'product-card';
                card.innerHTML = `
                    <h4>${product.name}</h4>
                    <p>价格: ¥${product.price}</p>
                    <p>库存: ${product.stock || 0}</p>
                `;
                container.appendChild(card);
            });
        }

        async function testCategory(categoryName, expectedId) {
            // 更新按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            currentCategory = categoryName;

            try {
                showResult(`正在测试分类: ${categoryName} (期望ID: ${expectedId})...`);

                const response = await axios.get(`/products/category/${expectedId}`);

                const result = `分类测试结果:
分类名称: ${categoryName}
使用ID: ${expectedId}
API成功: ${response.data.success}
产品数量: ${response.data.data?.length || 0}
消息: ${response.data.message || '无'}

响应数据:
${JSON.stringify(response.data, null, 2)}`;

                showResult(result);
                showProducts(response.data.data);

            } catch (error) {
                showResult(`测试失败: ${error.message}`);
                showProducts([]);
            }
        }

        // 页面加载时测试全部
        window.onload = function() {
            testCategory('all', 0);
        };
    </script>
</body>
</html>
