<!DOCTYPE html>
<html>
<head>
    <title>测试图片上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        input[type="file"] {
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>图片上传测试页面</h1>
    
    <div class="test-section">
        <h2>测试1: 新增产品图片上传（不带productId）</h2>
        <p>这个测试模拟新增产品时的图片上传，不传递productId参数。</p>
        <form id="form1" action="/products/uploadImage" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept=".jpg,.png,.jpeg" required>
            <br><br>
            <button type="submit">上传图片（新增产品模式）</button>
        </form>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试2: 编辑产品图片上传（带productId）</h2>
        <p>这个测试模拟编辑产品时的图片上传，传递productId=2参数。</p>
        <form id="form2" action="/products/uploadImage" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept=".jpg,.png,.jpeg" required>
            <input type="hidden" name="productId" value="2">
            <br><br>
            <button type="submit">上传图片（编辑产品模式）</button>
        </form>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-section">
        <h2>说明</h2>
        <ul>
            <li><strong>测试1</strong>：模拟新增产品时的图片上传，应该返回成功并提供图片URL</li>
            <li><strong>测试2</strong>：模拟编辑产品时的图片上传，应该更新数据库并返回成功</li>
            <li>支持的文件格式：JPG, PNG</li>
            <li>文件大小限制：2MB以内</li>
        </ul>
    </div>

    <script>
        // 处理表单提交
        document.getElementById('form1').addEventListener('submit', function(e) {
            handleFormSubmit(e, 'result1');
        });
        
        document.getElementById('form2').addEventListener('submit', function(e) {
            handleFormSubmit(e, 'result2');
        });
        
        function handleFormSubmit(e, resultId) {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            const resultDiv = document.getElementById(resultId);
            
            // 显示加载状态
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '上传中...';
            
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>上传成功！</strong><br>
                        图片URL: ${data.imageUrl}<br>
                        <img src="${data.imageUrl}" alt="上传的图片" style="max-width: 200px; margin-top: 10px;">
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>上传失败：</strong>${data.message}`;
                }
            })
            .catch(error => {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>请求失败：</strong>${error.message}`;
            });
        }
    </script>
</body>
</html>
