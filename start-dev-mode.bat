@echo off
echo ========================================
echo   SpringBoot开发模式启动 (热重载)
echo ========================================
echo.

REM 设置编码
chcp 65001 > nul

REM 设置项目路径
set PROJECT_DIR=%~dp0
cd /d "%PROJECT_DIR%"

echo 🔧 项目目录: %PROJECT_DIR%
echo 🔧 启动模式: 开发模式 (支持热重载)
echo.

REM 检查Maven
if not exist ".\apache-maven-3.9.4\bin\mvn.cmd" (
    echo ❌ 找不到Maven，请检查apache-maven-3.9.4目录
    pause
    exit /b 1
)

echo ✅ 找到Maven，开始启动...
echo.

REM 使用Maven的spring-boot:run目标启动（支持热重载）
echo 🚀 使用Maven启动SpringBoot应用程序...
echo    - 支持代码热重载
echo    - 支持静态资源实时更新
echo    - 端口: 8082
echo.

echo 🌟 应用程序启动中...
echo    访问地址: http://localhost:8082
echo    按 Ctrl+C 停止应用程序
echo.

REM 启动命令（类似IDEA的运行方式）
.\apache-maven-3.9.4\bin\mvn.cmd spring-boot:run ^
    -Dspring-boot.run.jvmArguments="-Dfile.encoding=UTF-8 -Dspring.profiles.active=dev -Dspring.output.ansi.enabled=always" ^
    -Dspring-boot.run.fork=false

echo.
echo 应用程序已停止
pause
