<!DOCTYPE html>
<html lang="en">
<head >
    <!--这是模板代码-->
    <!--从饿了吗网站直接复制组件的代码-->
    <!--在网站中ctrl+f快速搜索组件-->
    <meta charset="UTF-8">
    <!-- import CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
</head>
<body>
<!--el-这个前缀原本是不认可的，在运行时会被转换-->
<div id="app">
{{info}}
</div>
</body>
<!-- import Vue before Element -->
<script src="https://cdn.bootcdn.net/ajax/libs/axios/0.21.1/axios.min.js"></script>
<script src="https://unpkg.com/vue@2/dist/vue.js"></script>
<!-- import JavaScript -->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>
<script>
    new Vue({
        el: '#app',
        data: function() {
            return {
                info:"HelloEUI"
            }
        },
        methods:{

        }
    })
</script>
</html>